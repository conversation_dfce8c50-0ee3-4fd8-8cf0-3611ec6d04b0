export interface MapAPIConfig {
    baseUrl?: string;
    crtfckey?: string;
    timeout?: number;
}
export interface LayerContentSelectRequest {
    cntntsId: string;
    crtfckey?: string;
}
export interface LayerContentSelectResponse {
    code: number;
    message: string;
    result: {
        cntntsId: string;
        namespace: string;
        cntntsNm?: string;
        cntntsUrl?: string;
        [key: string]: any;
    };
}
export type MapClient = ReturnType<typeof createGeonMapClient>;
export declare function createGeonMapClient(config?: MapAPIConfig): {
    layer: {
        /** 레이어 컨텐츠 조회 */
        cnSelect: (params: LayerContentSelectRequest) => Promise<LayerContentSelectResponse>;
    };
};
export declare const defaultGeonMapClient: {
    layer: {
        /** 레이어 컨텐츠 조회 */
        cnSelect: (params: LayerContentSelectRequest) => Promise<LayerContentSelectResponse>;
    };
};
//# sourceMappingURL=map.d.ts.map