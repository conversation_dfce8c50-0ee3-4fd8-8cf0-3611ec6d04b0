import { BasemapDeleteRequest, BasemapInsertRequest, BasemapListRequest, BasemapListResponse, BasemapSelectRequest, BasemapUpdateRequest, CommonDetailCodeRequest, CommonGroupCodeRequest, LayerGroupInfoDeleteRequest, LayerGroupInfoExcelRequest, LayerGroupInfoInsertRequest, LayerGroupInfoListRequest, LayerGroupInfoListV2Request, LayerGroupInfoSelectRequest, LayerGroupInfoUpdateRequest, LayerShareDeleteRequest, LayerShareInsertRequest, LayerShareSelectByUserIdRequest, LayerShareSelectRequest, LayerShareUpdateRequest } from "./type/smt-type";
export interface LayerInfoSelectRequest {
    lyrId: string;
    sessionUserId?: string;
    crtfckey?: string;
}
export interface LayerInfoSelectResponse {
    code: number;
    message: string;
    result: {
        lyrId: string;
        lyrNm: string;
        lyrDc?: string;
        cntntsId: string;
        lyrClCode: string;
        lyrClSeCode: string;
        lyrTySeCode: string;
        svcTySeCode: string;
        onOffAt: string;
        cntmSeCode: string;
        flterCndCn?: string;
        dynmFlterCndCn?: string;
        symbolCndCn?: string;
        mapUrlParamtr?: string;
        [key: string]: any;
    };
}
export interface AddrSearchRequest {
    keyword: string;
    showMultipleResults?: boolean;
    targetSrid?: number;
    countPerPage?: number;
    currentPage?: number;
    crtfckey?: string;
}
export interface AddrSearchResponse {
    code: number;
    message: string;
    result: {
        list: Array<{
            roadAddr: string;
            buildName?: string;
            poiName?: string;
            buildLo: string;
            buildLa: string;
            [key: string]: any;
        }>;
        totalCount: number;
        pageIndex: number;
        pageSize: number;
    };
}
export interface SmtAPIConfig {
    baseUrl?: string;
    crtfckey?: string;
    timeout?: number;
}
export type SmtClient = ReturnType<typeof createGeonSmtClient>;
export declare function createGeonSmtClient(config?: SmtAPIConfig): {
    commonCode: {
        /** 공통 그룹 코드 조회 */
        getGroupCodes: (params: CommonGroupCodeRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 공통 상세 코드 조회 */
        getDetailCodes: (params: CommonDetailCodeRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
    };
    layerShare: {
        /** 레이어 공유 등록 */
        insert: (params: LayerShareInsertRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 레이어 공유 수정 */
        update: (params: LayerShareUpdateRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 레이어 공유 삭제 */
        delete: (params: LayerShareDeleteRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 레이어 공유 조회 */
        select: (params: LayerShareSelectRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 사용자 ID로 공유된 레이어 조회 */
        selectByUserId: (params: LayerShareSelectByUserIdRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
    };
    layerGroupInfo: {
        /** 레이어 그룹 목록 조회 */
        list: (params: LayerGroupInfoListRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 레이어 그룹 등록 */
        insert: (params: LayerGroupInfoInsertRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 레이어 그룹 수정 */
        update: (params: LayerGroupInfoUpdateRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 레이어 그룹 삭제 */
        delete: (params: LayerGroupInfoDeleteRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 레이어 그룹 조회 */
        select: (params: LayerGroupInfoSelectRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 레이어 그룹 목록 엑셀 다운로드 */
        excel: (params: LayerGroupInfoExcelRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 레이어 그룹 목록 조회(v2) */
        listV2: (params: LayerGroupInfoListV2Request) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
    };
    layer: {
        /** 레이어 목록 조회 */
        infoList: (params: LayerGroupInfoListRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 레이어 상세 조회 */
        infoSelect: (params: LayerInfoSelectRequest) => Promise<LayerInfoSelectResponse>;
    };
    addr: {
        /** 주소 검색 */
        search: (params: AddrSearchRequest) => Promise<AddrSearchResponse>;
    };
    basemap: {
        /** 베이스맵 수정 */
        update: (params: BasemapUpdateRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 베이스맵 등록 */
        insert: (params: BasemapInsertRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 베이스맵 삭제 */
        delete: (params: BasemapDeleteRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 베이스맵 조회 */
        select: (params: BasemapSelectRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 베이스맵 목록 */
        list: (params: BasemapListRequest) => Promise<BasemapListResponse>;
    };
};
export declare const defaultGeonSmtClient: {
    commonCode: {
        /** 공통 그룹 코드 조회 */
        getGroupCodes: (params: CommonGroupCodeRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 공통 상세 코드 조회 */
        getDetailCodes: (params: CommonDetailCodeRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
    };
    layerShare: {
        /** 레이어 공유 등록 */
        insert: (params: LayerShareInsertRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 레이어 공유 수정 */
        update: (params: LayerShareUpdateRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 레이어 공유 삭제 */
        delete: (params: LayerShareDeleteRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 레이어 공유 조회 */
        select: (params: LayerShareSelectRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 사용자 ID로 공유된 레이어 조회 */
        selectByUserId: (params: LayerShareSelectByUserIdRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
    };
    layerGroupInfo: {
        /** 레이어 그룹 목록 조회 */
        list: (params: LayerGroupInfoListRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 레이어 그룹 등록 */
        insert: (params: LayerGroupInfoInsertRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 레이어 그룹 수정 */
        update: (params: LayerGroupInfoUpdateRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 레이어 그룹 삭제 */
        delete: (params: LayerGroupInfoDeleteRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 레이어 그룹 조회 */
        select: (params: LayerGroupInfoSelectRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 레이어 그룹 목록 엑셀 다운로드 */
        excel: (params: LayerGroupInfoExcelRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 레이어 그룹 목록 조회(v2) */
        listV2: (params: LayerGroupInfoListV2Request) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
    };
    layer: {
        /** 레이어 목록 조회 */
        infoList: (params: LayerGroupInfoListRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 레이어 상세 조회 */
        infoSelect: (params: LayerInfoSelectRequest) => Promise<LayerInfoSelectResponse>;
    };
    addr: {
        /** 주소 검색 */
        search: (params: AddrSearchRequest) => Promise<AddrSearchResponse>;
    };
    basemap: {
        /** 베이스맵 수정 */
        update: (params: BasemapUpdateRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 베이스맵 등록 */
        insert: (params: BasemapInsertRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 베이스맵 삭제 */
        delete: (params: BasemapDeleteRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 베이스맵 조회 */
        select: (params: BasemapSelectRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 베이스맵 목록 */
        list: (params: BasemapListRequest) => Promise<BasemapListResponse>;
    };
};
//# sourceMappingURL=smt.d.ts.map