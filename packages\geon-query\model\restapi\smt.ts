// 정제된 basemap API (GET/POST 구분 및 타입 매칭)

import { API_TYPE, apiHelper } from "../utils/geonAPI";
import {
  BasemapDeleteRequest,
  BasemapInsertRequest,
  BasemapListRequest,
  BasemapListResponse,
  BasemapSelectRequest,
  BasemapUpdateRequest,
  CommonDetailCodeRequest,
  CommonGroupCodeRequest,
  LayerGroupInfoDeleteRequest,
  LayerGroupInfoExcelRequest,
  LayerGroupInfoInsertRequest,
  LayerGroupInfoListRequest,
  LayerGroupInfoListV2Request,
  LayerGroupInfoSelectRequest,
  LayerGroupInfoUpdateRequest,
  LayerShareDeleteRequest,
  LayerShareInsertRequest,
  LayerShareSelectByUserIdRequest,
  LayerShareSelectRequest,
  LayerShareUpdateRequest,
} from "./type/smt-type";

// 추가 API 타입들
export interface LayerInfoSelectRequest {
  lyrId: string;
  sessionUserId?: string;
  crtfckey?: string;
}

export interface LayerInfoSelectResponse {
  code: number;
  message: string;
  result: {
    lyrId: string;
    lyrNm: string;
    lyrDc?: string;
    cntntsId: string;
    lyrClCode: string;
    lyrClSeCode: string;
    lyrTySeCode: string;
    svcTySeCode: string;
    onOffAt: string;
    cntmSeCode: string;
    flterCndCn?: string;
    dynmFlterCndCn?: string;
    symbolCndCn?: string;
    mapUrlParamtr?: string;
    [key: string]: any;
  };
}

export interface AddrSearchRequest {
  keyword: string;
  showMultipleResults?: boolean;
  targetSrid?: number;
  countPerPage?: number;
  currentPage?: number;
  crtfckey?: string;
}

export interface AddrSearchResponse {
  code: number;
  message: string;
  result: {
    list: Array<{
      roadAddr: string;
      buildName?: string;
      poiName?: string;
      buildLo: string;
      buildLa: string;
      [key: string]: any;
    }>;
    totalCount: number;
    pageIndex: number;
    pageSize: number;
  };
}

const type: API_TYPE = "smt";

// 설정 타입
export interface SmtAPIConfig {
  baseUrl?: string;
  crtfckey?: string;
  timeout?: number;
}
//타입 선언용 returnType 정의
export type SmtClient = ReturnType<typeof createGeonSmtClient>;
// 동적 API 클라이언트 생성 함수
export function createGeonSmtClient(config: SmtAPIConfig = {}) {
  const { baseUrl, crtfckey } = config;

  const api = apiHelper({ type, baseUrl, crtfckey });
  return {
    // 예: commonCode 그대로 유지
    commonCode: {
      /** 공통 그룹 코드 조회 */
      getGroupCodes: api.get<CommonGroupCodeRequest>("/cmmn/code/group"),
      /** 공통 상세 코드 조회 */
      getDetailCodes: api.get<CommonDetailCodeRequest>("/cmmn/code/detail"),
    },

    // 레이어 공유 API
    layerShare: {
      /** 레이어 공유 등록 */
      insert: api.post<LayerShareInsertRequest>("/layer/share/insert"),
      /** 레이어 공유 수정 */
      update: api.post<LayerShareUpdateRequest>("/layer/share/update"),
      /** 레이어 공유 삭제 */
      delete: api.post<LayerShareDeleteRequest>("/layer/share/delete"),
      /** 레이어 공유 조회 */
      select: api.get<LayerShareSelectRequest>("/layer/share/select"),
      /** 사용자 ID로 공유된 레이어 조회 */
      selectByUserId: api.get<LayerShareSelectByUserIdRequest>(
        "/layer/share/select/userId",
      ),
    },

    // 레이어 그룹 정보 API
    layerGroupInfo: {
      /** 레이어 그룹 목록 조회 */
      list: api.get<LayerGroupInfoListRequest>("/lyrgrp/info/list"),
      /** 레이어 그룹 등록 */
      insert: api.post<LayerGroupInfoInsertRequest>("/lyrgrp/info/insert"),
      /** 레이어 그룹 수정 */
      update: api.post<LayerGroupInfoUpdateRequest>("/lyrgrp/info/update"),
      /** 레이어 그룹 삭제 */
      delete: api.post<LayerGroupInfoDeleteRequest>("/lyrgrp/info/delete"),
      /** 레이어 그룹 조회 */
      select: api.get<LayerGroupInfoSelectRequest>("/lyrgrp/info/select"),
      /** 레이어 그룹 목록 엑셀 다운로드 */
      excel: api.get<LayerGroupInfoExcelRequest>("/lyrgrp/info/excel"),
      /** 레이어 그룹 목록 조회(v2) */
      listV2: api.get<LayerGroupInfoListV2Request>("/lyrgrp/info/v2/list"),
    },

    // 레이어 API
    layer: {
      /** 레이어 목록 조회 */
      infoList: api.get<LayerGroupInfoListRequest>("/layer/info/list"),
      /** 레이어 상세 조회 */
      infoSelect: api.get<LayerInfoSelectRequest, LayerInfoSelectResponse>("/layer/info/select"),
    },

    // 주소 검색 API
    addr: {
      /** 주소 검색 */
      search: api.get<AddrSearchRequest, AddrSearchResponse>("/addr/search"),
    },

    // 베이스맵 API
    basemap: {
      /** 베이스맵 수정 */
      update: api.post<BasemapUpdateRequest>("/basemap/update"),
      /** 베이스맵 등록 */
      insert: api.post<BasemapInsertRequest>("/basemap/insert"),
      /** 베이스맵 삭제 */
      delete: api.post<BasemapDeleteRequest>("/basemap/delete"),
      /** 베이스맵 조회 */
      select: api.get<BasemapSelectRequest>("/basemap/select"),
      /** 베이스맵 목록 */
      list: api.get<BasemapListRequest, BasemapListResponse>("/basemap/list"),
    },
  };
}
export const defaultGeonSmtClient = createGeonSmtClient();
