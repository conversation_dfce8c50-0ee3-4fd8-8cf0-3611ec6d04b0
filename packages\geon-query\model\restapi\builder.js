import { apiHelper } from "../utils/geonAPI";
const type = "builder";
// 동적 API 클라이언트 생성 함수
export function createGeonBuilderClient(config = {}) {
    const { baseUrl, crtfckey } = config;
    const api = apiHelper({ type, baseUrl, crtfckey });
    return {
        // 레이어 API
        layer: {
            /** 레이어 목록 조회 */
            infoList: api.get("/builder/layer/info/list"),
            /** 레이어 속성 컬럼 조회 */
            attributesColumn: api.get("/builder/layer/attributes/column"),
            /** 레이어 속성 데이터 조회 */
            attributesSelect: api.post("/builder/layer/attributes/select"),
            /** 레이어 속성 개수 조회 */
            attributesCount: api.post("/builder/layer/attributes/count"),
        },
    };
}
export const defaultGeonBuilderClient = createGeonBuilderClient();
