/** @Todo : analysis-type 만들고 import, fetcher import */
import { apiHelper } from "../utils/geonAPI";
const type = "analysis";
// 동적 API 클라이언트 생성 함수
export function createGeonAnalysisClient(config = {}) {
    const { baseUrl, crtfckey } = config;
    const api = apiHelper({ type, baseUrl, crtfckey });
    return {
        fileDownload: {
            /** ### 레이어 파일 다운로드 (e.g. .csv, .zip, .geojson, .kml) */
            layerFileDownload: api.post("/layer/file/download", "application/json", "blob"),
        },
    };
}
