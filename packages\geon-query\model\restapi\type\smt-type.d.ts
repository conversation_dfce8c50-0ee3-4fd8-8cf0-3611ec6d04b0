export interface LayerGroupInfoListRequest {
    crtfckey?: string;
    lyrGrpNm?: string;
    userId?: string;
}
export interface LayerGroupInfoInsertRequest {
    crtfckey: string;
    userId: string;
    lyrGrpNm: string;
}
export interface LayerGroupInfoUpdateRequest {
    groupNm: string;
    groupDc?: string;
    lyrGroupId: string;
    othbcAt?: string;
    userId: string;
    crtfckey?: string;
}
export interface LayerGroupInfoDeleteRequest {
    crtfckey: string;
    lyrGrpId: string;
    userId: string;
}
export interface LayerGroupInfoSelectRequest {
    crtfckey: string;
    lyrGrpId: string;
}
export interface LayerGroupInfoExcelRequest {
    crtfckey: string;
    userId?: string;
    groupNm?: string;
}
export interface LayerGroupInfoListV2Request {
    crtfckey?: string;
    userId?: string;
    pageIndex?: number;
    pageSize?: number;
    othbcAt?: string;
}
export interface LayerShareInsertRequest {
    crtfckey?: string;
    layerId: string;
    userId: string;
    shareGrant: string;
}
export interface LayerShareDeleteRequest {
    crtfckey?: string;
    layerShareId: string;
}
export interface LayerShareUpdateRequest {
    crtfckey?: string;
    layerShareId: string;
    shareGrant: string;
}
export interface LayerShareSelectRequest {
    crtfckey?: string;
    layerId: string;
}
export interface LayerShareSelectByUserIdRequest {
    crtfckey?: string;
    userId: string;
}
type LyrStleCode = "M" | "F" | "T";
export interface CommonGroupCodeRequest {
    clCode?: string;
    crtfckey?: string;
    groupCode?: string;
}
export interface CommonDetailCodeRequest {
    clCode?: string;
    crtfckey?: string;
    groupCode?: string;
}
export interface BasemapUpdateRequest {
    bcrnMapClCode: string;
    bcrnMapId: string;
    bcrnMapNm: string;
    bcrnMapUseAt: "Y" | "N";
    crtfckey?: string;
    lyrStleCode: LyrStleCode;
    mapUrl: string;
    mapUrlParamtr?: string;
    registerId: string;
}
export interface BasemapSelectRequest {
    bcrnMapId?: string;
    crtfckey?: string;
}
export interface BasemapListRequest {
    bcrnMapClCode?: string;
    bcrnMapNm?: string;
    bcrnMapUseAt?: "Y" | "N";
    crtfckey?: string;
    imageAt?: "Y" | "N";
    lyrStleCode?: LyrStleCode;
    pageIndex: number;
    pageSize: number;
}
export interface BasemapListResponse {
    code: number;
    message: string;
    result: {
        pageInfo: {
            pageSize: number;
            pageIndex: number;
            totalCount: number;
        };
        list: BasemapItem[];
    };
}
export interface BasemapItem {
    bcrnMapId: string;
    bcrnMapNm: string;
    bcrnMapUseAt: string;
    bcrnMapClCode: string;
    bcrnMapClCodeNm: string;
    lyrStleCode: string;
    lyrStleCodeNm: string;
    mapUrl: string;
    mapUrlparamtr: string;
    base64: string;
    registerId: string;
    registDt: string;
    updusrId: string;
    updtDt: string;
}
export interface BasemapInsertRequest {
    bcrnMapClCode: string;
    bcrnMapNm: string;
    bcrnMapUseAt: "Y" | "N";
    crtfckey?: string;
    lyrStleCode: LyrStleCode;
    mapUrl: string;
    mapUrlParamtr?: string;
    registerId: string;
}
export interface BasemapDeleteRequest {
    bcrnMapId: string;
    crtfckey?: string;
}
export {};
//# sourceMappingURL=smt-type.d.ts.map