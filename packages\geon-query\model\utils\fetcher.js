import { Log } from "./Log";
/** ### 응답 형식 처리를 위한 헬퍼 함수 (e.g. json, blob 등) */
const responseProcessors = {
    json: (response) => response.json(),
    blob: async (response) => {
        const blob = await response.blob();
        return { headers: response.headers, blob };
    },
};
export const fetcher = {
    get: async (url, params, responseContentType = "json") => {
        const queryString = params
            ? Object.entries(params)
                .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
                .join("&")
            : "";
        const fullUrl = queryString
            ? url.includes("?")
                ? `${url}&${queryString}`
                : `${url}?${queryString}`
            : url;
        Log.logRequest("GET", fullUrl);
        try {
            const res = await fetch(fullUrl);
            const data = await responseProcessors[responseContentType](res);
            Log.logResponse("GET", fullUrl, data);
            return data;
        }
        catch (error) {
            Log.logError("GET", fullUrl, error);
            throw error;
        }
    },
    post: async (url, body, requestContentType = "application/json", responseContentType = "json") => {
        Log.logRequest("POST", url, body);
        try {
            const headers = {
                "Content-Type": requestContentType,
            };
            const encodedBody = requestContentType === "application/json"
                ? JSON.stringify(body)
                : new URLSearchParams(body).toString();
            const res = await fetch(url, {
                method: "POST",
                headers,
                body: encodedBody,
            });
            const data = await responseProcessors[responseContentType](res);
            Log.logResponse("POST", url, data);
            return data;
        }
        catch (error) {
            Log.logError("POST", url, error);
            throw error;
        }
    },
};
