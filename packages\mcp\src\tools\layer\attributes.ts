import { z } from 'zod';
import { createMCPTool } from '../shared/utils.js';
import { createGeonBuilderClient } from '@geon-query/model/restapi/builder';
import type { MCPToolDefinition, AISDKToolWrapper } from '../shared/types.js';

/**
 * 레이어 속성 개수 조회 도구
 */
export const getLayerAttributesCountTool: AISDKToolWrapper = {
  description: `레이어의 속성 데이터 개수를 조회합니다. 필터 조건을 적용하여 특정 조건에 맞는 데이터의 개수를 확인할 수 있습니다.

기능:
- 레이어의 전체 데이터 개수 조회
- 속성 필터 조건을 적용한 데이터 개수 조회
- 공간 필터 조건을 적용한 데이터 개수 조회
- 필터 조건 자동 정규화

사용 예시:
- 전체 개수: {"typeName": "Wgeontest3:L100000249"}
- 속성 필터: {"typeName": "Wgeontest3:L100000249", "attributeFilter": "a1 like '%종로구%'"}
- 복합 필터: {"typeName": "Wgeontest3:L100000249", "attributeFilter": "a1 like '%종로구%' AND a2 < '1995-02-19'"}`,

  parameters: z.object({
    typeName: z.string().describe("레이어 타입명 (예: Wgeontest3:L100000249)"),
    attributeFilter: z
      .string()
      .optional()
      .describe("속성 필터 내용 (예: a1 like '%종로구%' AND a2 < '1995-02-19')"),
    spatialFilter: z
      .string()
      .optional()
      .describe("공간 필터 내용 (예: POLYGON((190000 540000, 200000 540000, 200000 550000, 190000 550000, 190000 540000)))"),
    spatialFilterSrid: z
      .string()
      .optional()
      .describe("공간 필터 SRID (예: 5186)"),
  }),

  execute: async ({
    typeName,
    attributeFilter,
    spatialFilter,
    spatialFilterSrid,
  }) => {
    try {
      // geon-query/model 클라이언트 생성
      const apiKey = process.env.GEON_API_KEY || "UxizIdSqCePz93ViFt8ghZFFJuOzvUp0";
      const baseUrl = process.env.GEON_API_BASE_URL || "https://city.geon.kr/api/";

      const builderClient = createGeonBuilderClient({
        baseUrl,
        crtfckey: apiKey,
      });

      // API 호출 파라미터 구성
      const requestParams = {
        typeName: typeName,
        crtfckey: apiKey,
      } as any;

      // 선택적 파라미터들 추가
      if (attributeFilter && attributeFilter.trim() !== "") {
        // 이스케이프 문자 제거 및 CQL 형식 정규화
        const cleanFilter = attributeFilter
          .trim()
          .replace(/\\"/g, '"') // 이스케이프된 따옴표 제거
          .replace(/"([^"]+)"/g, "$1") // 속성명 주변 따옴표 제거
          .replace(/([a-zA-Z0-9_]+)([><=!]+)([0-9]+)/g, "$1 $2 '$3'") // 숫자 값에 따옴표 추가 및 공백 정규화
          .replace(/([a-zA-Z0-9_]+)([><=!]+)'([^']+)'/g, "$1 $2 '$3'") // 이미 따옴표가 있는 경우 공백만 정규화
          .replace(/\s+/g, " "); // 여러 공백을 하나로 정규화

        requestParams.filter = cleanFilter;
      }

      if (spatialFilter && spatialFilter.trim() !== "") {
        requestParams.spatialFilter = spatialFilter.trim();
      }

      if (spatialFilterSrid && spatialFilterSrid.trim() !== "") {
        requestParams.spatialFilterSrid = spatialFilterSrid.trim();
      }

      // 레이어 속성 개수 조회 API 호출
      const data = await builderClient.layer.attributesCount(requestParams);

      if (!data) {
        return { 
          success: false,
          error: "레이어 속성개수 조회 실패: 응답 데이터가 없습니다." 
        };
      }

      return {
        success: true,
        typeName,
        count: data.count || data.result || data,
        attributeFilter,
        spatialFilter,
        spatialFilterSrid,
        description: `레이어 ${typeName}의 속성 데이터 개수: ${data.count || data.result || data}개`,
      };
    } catch (error: any) {
      console.error("레이어 속성개수 조회 실패:", error);
      return { 
        success: false,
        error: `레이어 속성개수 조회 실패: ${error.message}` 
      };
    }
  },

  experimental_toToolResultContent: (result: any) => {
    if (result.error) {
      return [{ type: "text" as const, text: result.error }];
    }

    let filterInfo = "";
    if (result.attributeFilter) {
      filterInfo += `\n속성 필터: ${result.attributeFilter}`;
    }
    if (result.spatialFilter) {
      filterInfo += `\n공간 필터: 적용됨`;
    }

    return [
      {
        type: "text" as const,
        text: `${result.description}${filterInfo}`,
      },
    ];
  },
};

/**
 * MCP 도구 정의
 */
export const getLayerAttributesCountDefinition: MCPToolDefinition = createMCPTool(
  "getLayerAttributesCount",
  getLayerAttributesCountTool,
  {
    title: "레이어 속성 개수 조회",
    readOnlyHint: true,
    destructiveHint: false,
    idempotentHint: true,
  }
);