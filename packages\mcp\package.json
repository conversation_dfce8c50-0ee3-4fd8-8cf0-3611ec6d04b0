{"name": "@geon/mcp", "version": "0.1.5", "main": "index.js", "type": "module", "bin": {"geon-mcp": "./dist/index.js"}, "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('dist/index.js', '755')\"", "start": "node dist/index.js", "tsc:watch": "tsc -w --preserveWatchOutput", "node:watch": "nodemon --watch dist --ext js,json --delay 200ms dist/index.js", "dev": "run-p tsc:watch node:watch", "inspector": "npx @modelcontextprotocol/inspector dist/index.js", "prepublishOnly": "npm run build"}, "files": ["dist"], "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@geon-query/model": "workspace:*", "@modelcontextprotocol/sdk": "^1.13.2", "@types/jsonwebtoken": "^9.0.10", "dotenv": "^17.2.1", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "jwks-client": "^2.0.5", "zod": "^3.25.67", "zod-to-json-schema": "^3.24.1"}, "devDependencies": {"@types/express": "^5.0.1", "@types/node": "^20.10.6", "nodemon": "^3.1.10", "npm-run-all": "^4.1.5", "typescript": "^5.7.3"}}