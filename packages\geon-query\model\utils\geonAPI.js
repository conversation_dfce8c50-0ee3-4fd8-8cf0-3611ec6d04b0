import { fetcher } from "./fetcher";
export const BASE_URL = "https://city.geon.kr/api/";
export const crtfckey = "UxizIdSqCePz93ViFt8ghZFFJuOzvUp0";
export const WMS_URL = BASE_URL + "map/api/map/wms";
export const WFS_URL = BASE_URL + "map/api/map/wfs";
/**
 * API endpoint factory helper
 *
 * @example
 * ```typescript
 * // 기본 request 타입이 any 로 설정된 api helper 생성
 * const api = apiHelper<any>({ type, baseUrl, crtfckey });
 * ```
 *
 * @template D default 요청 파라미터 타입
 */
export function apiHelper({ type, baseUrl, crtfckey, }) {
    return {
        /**
         * `fetcher.get` factory 함수
         *
         * @example
         * ```typescript
         * const api = apiHelper<any>({ ... });
         * // 요청 파라미터가 { crtfckey?: string } 이고, 응답 형태는 기본(APIResponse), endpoint 주소를 "/endpoint/url"로 갖는 fetcher.get
         * api.get<{ crtfckey?: string }>("/endpoint/url");
         * ```
         * @template P 요청 파라미터 타입
         * @template R 응답 타입
         *
         * @param endpoint endpoint url
         * @returns `Promise<R>`
         */
        get: (endpoint) => (params) => fetcher.get(apiUrl({ endpoint, type, baseUrl, crtfckey }), params),
        /**
         * `fetcher.post` factory 함수
         *
         * @example
         * ```typescript
         * const api = apiHelper<any>({ ... });
         * // 요청 Body 가 { crtfckey?: string } 이고, 응답 형태는 기본(APIResponse), endpoint 주소를 "/endpoint/url"로 갖는 fetcher.post
         * api.post<{ crtfckey?: string }>("/endpoint/url");
         * ```
         * @template P 요청 body 타입
         * @template R 응답 타입
         *
         * @param endpoint endpoint url
         * @returns `Promise<R>`
         */
        post: (endpoint, requestContentType = "application/json", responseContentType = "json") => (params) => fetcher.post(apiUrl({ endpoint, type, baseUrl, crtfckey }), params, requestContentType, responseContentType),
    };
}
/**
 * GEON API URL을 생성하는 함수
 * @param {ApiUrlParams} params - API URL 생성 파라미터
 * @returns {string} 완성된 API URL
 * @example
 * ```typescript
 * // 기본 사용
 * const url1 = apiUrl({ endpoint: "/search", type: "addrgeo" });
 * // → "https://city.geon.kr/api/addrgeo/search?crtfckey=UxizIdSqCePz93ViFt8ghZFFJuOzvUp0"
 *
 * // 커스텀 설정 사용
 * const url2 = apiUrl({
 *   endpoint: "/address/bld",
 *   type: "addrgeo",
 *   baseUrl: "https://dev.geon.kr/api/",
 *   crtfckey: "custom-key"
 * });
 * // → "https://dev.geon.kr/api/addrgeo/address/bld?crtfckey=custom-key"
 * ```
 */
export const apiUrl = ({ endpoint, type, baseUrl = BASE_URL, crtfckey: apiKey = crtfckey, // 파라미터 이름을 apiKey로 변경
 }) => `${baseUrl}${type}${endpoint}?crtfckey=${apiKey}`;
