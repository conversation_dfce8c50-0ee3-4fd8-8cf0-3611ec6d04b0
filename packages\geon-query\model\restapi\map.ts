import { API_TYPE, apiHelper } from "../utils/geonAPI";

const type: API_TYPE = "map";

// 설정 타입
export interface MapAPIConfig {
  baseUrl?: string;
  crtfckey?: string;
  timeout?: number;
}

// 레이어 컨텐츠 조회 요청 타입
export interface LayerContentSelectRequest {
  cntntsId: string;
  crtfckey?: string;
}

// 레이어 컨텐츠 조회 응답 타입
export interface LayerContentSelectResponse {
  code: number;
  message: string;
  result: {
    cntntsId: string;
    namespace: string;
    cntntsNm?: string;
    cntntsUrl?: string;
    [key: string]: any;
  };
}

//타입 선언용 returnType 정의
export type MapClient = ReturnType<typeof createGeonMapClient>;

// 동적 API 클라이언트 생성 함수
export function createGeonMapClient(config: MapAPIConfig = {}) {
  const { baseUrl, crtfckey } = config;
  const api = apiHelper({ type, baseUrl, crtfckey });

  return {
    // 레이어 API
    layer: {
      /** 레이어 컨텐츠 조회 */
      cnSelect: api.get<LayerContentSelectRequest, LayerContentSelectResponse>("/layer/cn/select"),
    },
  };
}

export const defaultGeonMapClient = createGeonMapClient();
