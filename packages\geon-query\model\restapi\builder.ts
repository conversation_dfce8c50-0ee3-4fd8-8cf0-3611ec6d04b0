import { API_TYPE, apiHelper } from "../utils/geonAPI";

const type: API_TYPE = "builder" as API_TYPE;

// 설정 타입
export interface BuilderAPIConfig {
  baseUrl?: string;
  crtfckey?: string;
  timeout?: number;
}

// 레이어 목록 요청 타입
export interface LayerInfoListRequest {
  userId: string;
  searchTxt?: string;
  lyrTySeCode?: string;
  holdDataSeCode?: string;
  pageIndex?: string;
  pageSize?: string;
  crtfckey?: string;
}

// 레이어 목록 응답 타입
export interface LayerInfoListResponse {
  code: number;
  message: string;
  result: {
    list: Array<{
      lyrId: string;
      lyrNm: string;
      lyrTySeCode: string;
      lyrTySeCodeNm: string;
      holdDataSeCode: string;
      holdDataSeCodeNm: string;
      cntntsId: string;
      namespace: string;
      geometryType: string;
      [key: string]: any;
    }>;
    totalCount: number;
    pageIndex: number;
    pageSize: number;
  };
}

// 레이어 속성 컬럼 요청 타입
export interface LayerAttributesColumnRequest {
  typeName: string;
  crtfckey?: string;
}

// 레이어 속성 컬럼 응답 타입
export interface LayerAttributesColumnResponse {
  code: number;
  message: string;
  result: Array<{
    columnName: string;
    columnType: string;
    columnComment?: string;
    [key: string]: any;
  }>;
}

// 레이어 속성 데이터 요청 타입
export interface LayerAttributesSelectRequest {
  typeName: string;
  pageIndex?: number;
  pageSize?: number;
  crtfckey?: string;
}

// 레이어 속성 데이터 응답 타입
export interface LayerAttributesSelectResponse {
  code: number;
  message: string;
  result: {
    list: Array<Record<string, any>>;
    totalCount: number;
    pageIndex: number;
    pageSize: number;
  };
}

// 레이어 속성 개수 요청 타입
export interface LayerAttributesCountRequest {
  typeName: string;
  filter?: string;
  crtfckey?: string;
}

// 레이어 속성 개수 응답 타입
export interface LayerAttributesCountResponse {
  code: number;
  message: string;
  result: {
    count: number;
  };
}

//타입 선언용 returnType 정의
export type BuilderClient = ReturnType<typeof createGeonBuilderClient>;

// 동적 API 클라이언트 생성 함수
export function createGeonBuilderClient(config: BuilderAPIConfig = {}) {
  const { baseUrl, crtfckey } = config;
  const api = apiHelper({ type, baseUrl, crtfckey });

  return {
    // 레이어 API
    layer: {
      /** 레이어 목록 조회 */
      infoList: api.get<LayerInfoListRequest, LayerInfoListResponse>("/builder/layer/info/list"),
      
      /** 레이어 속성 컬럼 조회 */
      attributesColumn: api.get<LayerAttributesColumnRequest, LayerAttributesColumnResponse>("/builder/layer/attributes/column"),
      
      /** 레이어 속성 데이터 조회 */
      attributesSelect: api.post<LayerAttributesSelectRequest, LayerAttributesSelectResponse>("/builder/layer/attributes/select"),
      
      /** 레이어 속성 개수 조회 */
      attributesCount: api.post<LayerAttributesCountRequest, LayerAttributesCountResponse>("/builder/layer/attributes/count"),
    },
  };
}

export const defaultGeonBuilderClient = createGeonBuilderClient();
