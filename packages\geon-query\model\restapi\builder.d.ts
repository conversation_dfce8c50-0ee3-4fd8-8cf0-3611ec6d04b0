export interface BuilderAPIConfig {
    baseUrl?: string;
    crtfckey?: string;
    timeout?: number;
}
export interface LayerInfoListRequest {
    userId: string;
    searchTxt?: string;
    lyrTySeCode?: string;
    holdDataSeCode?: string;
    pageIndex?: string;
    pageSize?: string;
    crtfckey?: string;
}
export interface LayerInfoListResponse {
    code: number;
    message: string;
    result: {
        list: Array<{
            lyrId: string;
            lyrNm: string;
            lyrTySeCode: string;
            lyrTySeCodeNm: string;
            holdDataSeCode: string;
            holdDataSeCodeNm: string;
            cntntsId: string;
            namespace: string;
            geometryType: string;
            [key: string]: any;
        }>;
        totalCount: number;
        pageIndex: number;
        pageSize: number;
    };
}
export interface LayerAttributesColumnRequest {
    typeName: string;
    crtfckey?: string;
}
export interface LayerAttributesColumnResponse {
    code: number;
    message: string;
    result: Array<{
        columnName: string;
        columnType: string;
        columnComment?: string;
        [key: string]: any;
    }>;
}
export interface LayerAttributesSelectRequest {
    typeName: string;
    pageIndex?: number;
    pageSize?: number;
    crtfckey?: string;
}
export interface LayerAttributesSelectResponse {
    code: number;
    message: string;
    result: {
        list: Array<Record<string, any>>;
        totalCount: number;
        pageIndex: number;
        pageSize: number;
    };
}
export interface LayerAttributesCountRequest {
    typeName: string;
    filter?: string;
    crtfckey?: string;
}
export interface LayerAttributesCountResponse {
    code: number;
    message: string;
    result: {
        count: number;
    };
}
export type BuilderClient = ReturnType<typeof createGeonBuilderClient>;
export declare function createGeonBuilderClient(config?: BuilderAPIConfig): {
    layer: {
        /** 레이어 목록 조회 */
        infoList: (params: LayerInfoListRequest) => Promise<LayerInfoListResponse>;
        /** 레이어 속성 컬럼 조회 */
        attributesColumn: (params: LayerAttributesColumnRequest) => Promise<LayerAttributesColumnResponse>;
        /** 레이어 속성 데이터 조회 */
        attributesSelect: (params: LayerAttributesSelectRequest) => Promise<LayerAttributesSelectResponse>;
        /** 레이어 속성 개수 조회 */
        attributesCount: (params: LayerAttributesCountRequest) => Promise<LayerAttributesCountResponse>;
    };
};
export declare const defaultGeonBuilderClient: {
    layer: {
        /** 레이어 목록 조회 */
        infoList: (params: LayerInfoListRequest) => Promise<LayerInfoListResponse>;
        /** 레이어 속성 컬럼 조회 */
        attributesColumn: (params: LayerAttributesColumnRequest) => Promise<LayerAttributesColumnResponse>;
        /** 레이어 속성 데이터 조회 */
        attributesSelect: (params: LayerAttributesSelectRequest) => Promise<LayerAttributesSelectResponse>;
        /** 레이어 속성 개수 조회 */
        attributesCount: (params: LayerAttributesCountRequest) => Promise<LayerAttributesCountResponse>;
    };
};
//# sourceMappingURL=builder.d.ts.map