// packages/query/reactQuery5/utils/Log.ts
export class Log {
    static EXCLUDED_URLS = ["selectRainFallRegDate", "xxx"];
    static logRequest(method, url, data) {
        if (this.isExcluded(url))
            return;
        const logs = [
            `%c[REQ]%c %c[${method.toUpperCase()}] %c${url}`,
            "color:white; background:skyblue;",
            "",
            "color: red",
            "color: black",
            data || "데이터 없음",
        ];
        console.log(...logs);
    }
    static logResponse(method, url, res) {
        if (this.isExcluded(url))
            return;
        const logs = [
            `%c[RES]%c %c[${method.toUpperCase()}] %c${url}`,
            "color:white; background:blue;",
            "",
            "color: red",
            "color: black",
            res,
        ];
        console.log(...logs);
    }
    static logError(method, url, error) {
        const logs = [
            `%c[REQ]%c %c[ERROR] %c${url}`,
            "color:white; background:blue;",
            "",
            "color:red",
            "color:red",
            error,
        ];
        console.error(...logs);
    }
    static isExcluded(url) {
        return this.EXCLUDED_URLS.some((ex) => url.includes(ex));
    }
}
