"use client";

import {
	Too<PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	Too<PERSON><PERSON>eader,
	ToolInput,
	ToolOutput,
} from "@workspace/ui/components/ai-elements/tool";
import { Map as ODFMap } from "@geon-map/react-odf";
import { DownloadWidget } from "@geon-map/react-ui/components";
import {
	Conversation,
	ConversationContent,
} from "@workspace/ui/components/ai-elements/conversation";
import { Loader } from "@workspace/ui/components/ai-elements/loader";
import {
	Message,
	MessageContent,
} from "@workspace/ui/components/ai-elements/message";
import {
	PromptInput,
	PromptInputButton,
	PromptInputModelSelect,
	PromptInputModelSelectContent,
	PromptInputModelSelectItem,
	PromptInputModelSelectTrigger,
	PromptInputModelSelectValue,
	PromptInputSubmit,
	PromptInputTextarea,
	PromptInputToolbar,
	PromptInputTools,
} from "@workspace/ui/components/ai-elements/prompt-input";
import {
	Reasoning,
	ReasoningContent,
	ReasoningTrigger,
} from "@workspace/ui/components/ai-elements/reasoning";
import { Response } from "@workspace/ui/components/ai-elements/response";
import {
	ResizablePanelGroup,
	ResizablePanel,
	ResizableHandle,
} from "@workspace/ui/components/resizable";
import { useState } from "react";
import { useSession } from "next-auth/react";
import { Brain } from "lucide-react";
import RegionSelector from "@/components/region-selector";
import BasemapSelector from "@/components/basemap-selector";
import { UserProfile } from "@/components/user-profile";
import { lastAssistantMessageIsCompleteWithToolCalls } from "ai";
import { useChat } from "@ai-sdk/react";

const models = [
	{ name: "Qwen3 4B (GEON)", value: "Qwen3-4B" },
	{ name: "Qwen3 14B (GEON)", value: "Qwen3-14B" },
	{
		name: "GPT 5 Nano",
		value: "openai/gpt-5-nano",
	},
];

export default function Page() {
	const { data: session, status: sessionStatus } = useSession();
	const [input, setInput] = useState("");
	const [showHistory, setShowHistory] = useState(false);
	const [showChatMobile, setShowChatMobile] = useState(false);
	const [model, setModel] = useState<string>(
		models[0]?.value || "openai/gpt-5-nano",
	);
	const [reasoning, setReasoning] = useState(false);
	const { messages, sendMessage, status } = useChat({
		sendAutomaticallyWhen: lastAssistantMessageIsCompleteWithToolCalls,
	});

	// 로그인하지 않은 경우 로그인 페이지로 리디렉트
	if (sessionStatus === "loading") {
		return (
			<div className="flex h-screen w-screen items-center justify-center">
				Loading...
			</div>
		);
	}

	if (!session) {
		window.location.href = "/login";
		return null;
	}

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		if (input.trim()) {
			sendMessage(
				{ text: input },
				{
					body: {
						modelId: model,
						reasoning: reasoning,
					},
				},
			);
			setInput("");
		}
	};

	const ChatPanel = (
		<div className="flex flex-col h-full w-full border-l bg-background">
			{/* 채팅 헤더 */}
			<div className="flex items-center justify-between p-3 border-b">
				<div className="text-sm font-medium">AI 대화</div>
				<button
					type="button"
					className="rounded-md px-2 py-1 text-xs hover:bg-muted"
					onClick={() => setShowHistory((v) => !v)}
				>
					히스토리
				</button>
			</div>

			<Conversation className="h-full">
				<ConversationContent>
					{messages.map((message) => (
						<Message from={message.role} key={message.id}>
							<MessageContent>
								{message.parts.map((part, i) => {

									switch (part.type) {
										case "text":
											return (
												<Response key={`${message.id}-${i}`}>
													{part.text}
												</Response>
											);
										case "reasoning":
											return (
												<Reasoning
													key={`${message.id}-${i}`}
													className="w-full"
													isStreaming={status === "streaming"}
												>
													<ReasoningTrigger />
													<ReasoningContent>{part.text}</ReasoningContent>
												</Reasoning>
											);
										case "dynamic-tool":
											console.log(part);
											return (
												<Tool key={`${message.id}-${i}`}>
													<ToolHeader
														type={`tool-${part.toolName}`}
														state={part.state}
													/>
													<ToolContent>
														<ToolInput input={part.input} />
														<ToolOutput
															output={
																<Response>
																	{typeof part.output === "string" ? part.output : ""}
																</Response>
															}
															errorText={part.errorText}
														/>
													</ToolContent>
												</Tool>
											);
										default:
											return null;
									}
								})}
							</MessageContent>
						</Message>
					))}
					{status === "submitted" && <Loader />}
				</ConversationContent>
			</Conversation>

			<div className="flex p-4 border-t">
				<PromptInput onSubmit={handleSubmit}>
					<PromptInputTextarea
						onChange={(e) => setInput(e.target.value)}
						value={input}
						placeholder="메시지를 입력하세요"
					/>
					<PromptInputToolbar className="border-t">
						<PromptInputTools>
							<PromptInputButton
								variant={reasoning ? "default" : "ghost"}
								onClick={() => setReasoning(!reasoning)}
							>
								<Brain size={16} />
								<span>추론</span>
							</PromptInputButton>
							<PromptInputModelSelect
								onValueChange={(value) => {
									setModel(value);
								}}
								value={model}
							>
								<PromptInputModelSelectTrigger>
									<PromptInputModelSelectValue />
								</PromptInputModelSelectTrigger>
								<PromptInputModelSelectContent>
									{models.map((model) => (
										<PromptInputModelSelectItem
											key={model.value}
											value={model.value}
										>
											{model.name}
										</PromptInputModelSelectItem>
									))}
								</PromptInputModelSelectContent>
							</PromptInputModelSelect>
						</PromptInputTools>
						<PromptInputSubmit disabled={!input} status={status} />
					</PromptInputToolbar>
				</PromptInput>
			</div>
		</div>
	);

	return (
		<div className="h-svh w-full">
			<ResizablePanelGroup direction="horizontal" className="h-full">
				{/* 지도 패널 */}
				<ResizablePanel defaultSize={65} minSize={50} maxSize={80}>
					<div className="relative h-full overflow-hidden">
						<ODFMap className="absolute inset-0 h-full w-full">
							<DownloadWidget />
							<RegionSelector />
							<BasemapSelector />
						</ODFMap>

						{/* 사용자 프로필 */}
						<UserProfile />

						{/* 모바일 FAB: 채팅 열기 */}
						<button
							type="button"
							className="md:hidden fixed bottom-4 right-4 z-20 rounded-full bg-primary px-4 py-2 text-primary-foreground shadow"
							onClick={() => setShowChatMobile(true)}
						>
							채팅
						</button>
					</div>
				</ResizablePanel>

				{/* 리사이즈 핸들 */}
				<ResizableHandle withHandle className="hidden md:flex" />

				{/* 데스크톱 우측 채팅 패널 */}
				<ResizablePanel
					defaultSize={35}
					minSize={20}
					maxSize={50}
					className="hidden md:block"
				>
					{ChatPanel}
				</ResizablePanel>
			</ResizablePanelGroup>

			{/* 모바일 오버레이 채팅 */}
			{showChatMobile && (
				<div className="md:hidden fixed inset-0 z-30 bg-background">
					<div className="absolute inset-0">{ChatPanel}</div>
					<button
						type="button"
						className="absolute right-3 top-3 rounded-md border px-2 py-1 text-xs"
						onClick={() => setShowChatMobile(false)}
					>
						닫기
					</button>
				</div>
			)}

			{/* 히스토리 패널 (우측 슬라이드 오버레이) */}
			{showHistory && (
				<>
					{/* 백드롭 */}
					<button
						type="button"
						className="fixed inset-0 z-40 bg-black/20 backdrop-blur-sm"
						onClick={() => setShowHistory(false)}
						aria-label="히스토리 패널 닫기"
					/>
					{/* 히스토리 패널 */}
					<div className="fixed inset-y-0 right-0 z-50 w-[400px] border-l bg-background shadow-xl animate-in slide-in-from-right duration-300">
						<div className="flex h-full flex-col">
							{/* 헤더 */}
							<div className="flex items-center justify-between border-b p-4">
								<div className="font-semibold">대화 이력</div>
								<button
									type="button"
									className="rounded-md p-1 hover:bg-muted"
									onClick={() => setShowHistory(false)}
									aria-label="히스토리 패널 닫기"
								>
									<svg
										className="h-4 w-4"
										fill="none"
										stroke="currentColor"
										viewBox="0 0 24 24"
										aria-hidden="true"
									>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M6 18L18 6M6 6l12 12"
										/>
									</svg>
								</button>
							</div>

							{/* 콘텐츠 */}
							<div className="flex-1 overflow-y-auto p-4">
								<div className="space-y-3">
									{messages.length > 0 ? (
										messages.slice(-10).map((m, index) => (
											<div
												key={m.id}
												className="rounded-lg border p-3 hover:bg-muted/50 transition-colors"
											>
												<div className="text-xs text-muted-foreground mb-1">
													{m.role === "user" ? "사용자" : "AI"} • {index + 1}
													번째 메시지
												</div>
												<div className="text-sm line-clamp-3">
													{m.parts
														.find((p) => p.type === "text")
														?.text?.slice(0, 120) || "메시지"}
												</div>
											</div>
										))
									) : (
										<div className="text-center text-muted-foreground py-8">
											<div className="text-4xl mb-2">💬</div>
											<div>아직 대화가 없습니다</div>
											<div className="text-xs mt-1">
												메시지를 보내서 대화를 시작해보세요
											</div>
										</div>
									)}
								</div>
							</div>
						</div>
					</div>
				</>
			)}
		</div>
	);
}
