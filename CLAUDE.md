# CLAUDE.md

이 파일은 Claude Code (claude.ai/code)가 이 저장소에서 작업할 때 필요한 가이드라인을 제공합니다.

## 프로젝트 개요 및 마이그레이션 목표

이 프로젝트는 **레거시 챗봇 애플리케이션을 모던 모노레포 구조로 마이그레이션**하는 중장기 프로젝트입니다.

### 현재 상황
- `front-chat/` - **레거시 프로젝트** (AI SDK 4, 구식 지도 컴포넌트, 유지보수성/재사용성 문제)
- `apps/web/` - **새로운 메인 애플리케이션** (현재 2팩터 레이아웃: 좌측 지도, 우측 대화창, 로그인 구현 완료)

### 마이그레이션 목표
1. **front-chat의 모든 기능을 apps/web으로 완전 이전**
2. **지도 관련 컴포넌트** → `geon-map` 패키지 사용
3. **API 호출 로직** → `geon-query` 패키지 + AI SDK 5로 업그레이드
4. **MCP 서버 통합** → 도구 및 프롬프트 등록하여 활용
5. **UI 컴포넌트** → 모노레포의 `ui` 패키지 사용 (`geon-ui` 사용 안함)

## 저장소 구조

```
chatbot/
├── apps/
│   └── web/          # 새로운 메인 챗봇 애플리케이션 (Next.js 15)
├──    front-chat/   # 레거시 챗봇 애플리케이션 (마이그레이션 대상)
├── packages/
│   ├── geon-map/     # 지도 관련 유틸리티 (core, react/odf, react/ui)
│   ├── geon-query/   # 쿼리 처리 (model, reactQuery)
│   ├── geon-ui/      # React UI 컴포넌트 (사용 안함)
│   ├── mcp/          # Model Context Protocol 서버 구현
│   ├── config/       # 공통 설정 (eslint, tailwind, typescript)
│   └── ui/           # 기본 UI 컴포넌트 라이브러리 (메인 사용)
```

## 패키지 관리자 및 워크스페이스

- **패키지 관리자**: pnpm 10.4.1+
- **Node.js**: >=20 필수
- **모노레포 도구**: Turborepo (빌드 오케스트레이션)
- **워크스페이스 설정**: `pnpm-workspace.yaml`에서 패키지 구조 정의

## 주요 개발 명령어

### 빌드 및 개발
```bash
# 모든 앱의 개발 서버 시작
pnpm dev

# 모든 패키지 및 앱 빌드
pnpm build

# Turbopack을 사용한 개발 (더 빠름)
cd apps/web && pnpm dev
cd front-chat && pnpm dev --port 3000
```

### 코드 품질 및 린팅
```bash
# Biome을 사용한 모든 패키지 린팅
pnpm lint

# Biome을 사용한 코드 포맷팅
pnpm format

# 코드 이슈 확인 및 자동 수정
pnpm check:fix

# 모든 패키지 타입 체크
pnpm typecheck
```

### 데이터베이스 작업 (front-chat 앱)
```bash
cd front-chat
pnpm migrate        # 데이터베이스 마이그레이션 실행
pnpm build          # 빌드 전 마이그레이션 포함
```

### MCP 서버 개발
```bash
cd packages/mcp
pnpm build          # TypeScript를 dist/로 빌드
pnpm dev            # 감시 모드 개발
pnpm inspector      # MCP 서버 디버그 인스펙터
```

## 코드 스타일 설정

- **포맷터**: Biome (`biome.json`에서 설정)
- **들여쓰기**: 탭 (2스페이스 상당)
- **라인 너비**: 80자
- **따옴표**: JSX에는 이중 따옴표, 세미콜론 필수
- **Import 정리**: Biome을 통한 자동 정리

## 아키텍처 노트

### Next.js 애플리케이션
- `apps/web`과 `front-chat` 모두 Next.js 15+ 애플리케이션
- `apps/web`은 개발 시 Turbopack 사용
- `front-chat`은 AI SDK 통합 및 Drizzle ORM 데이터베이스 포함
- NextAuth 5.0 베타를 통한 인증 처리

### 공통 패키지
- 모든 패키지는 `@workspace/typescript-config`의 공통 설정으로 TypeScript 사용
- 워크스페이스 패키지는 package.json에서 `workspace:*`로 참조
- React 컴포넌트는 shadcn/ui 패턴과 Radix UI 프리미티브 사용
- 일부 geon-map 패키지에 Vitest 테스트 설정 포함

### Model Context Protocol (MCP)
- `packages/mcp/`에 커스텀 MCP 서버 구현
- 챗봇 애플리케이션을 위한 도구 통합 제공
- Express.js 및 JWT 인증으로 구축
- Geon Query 모델을 데이터 처리에 활용

### 개발 워크플로우
- 외부 패키지 저장소와의 Subtree 통합:
  ```bash
  git subtree pull --prefix=packages https://gitlab.geon.kr/geon-biz/magp/pakages.git dev --squash
  ```
- Turborepo가 빌드 의존성 및 캐싱 관리
- VS Code 워크스페이스에 권장 확장 프로그램 설정

## 마이그레이션 작업 시 고려사항

1. **지도 컴포넌트**: `front-chat`의 지도 관련 코드를 `geon-map` 패키지로 교체
2. **API 호출**: AI SDK 4에서 AI SDK 5로 업그레이드하며 `geon-query` 활용
3. **UI 컴포넌트**: `geon-ui` 대신 모노레포의 `ui` 패키지 사용
4. **MCP 통합**: 도구 및 프롬프트를 MCP 서버에 등록하여 챗봇 기능 확장