export type RequestContentType = "application/json" | "application/x-www-form-urlencoded";
export type ResponseContentType = "json" | "blob";
export declare const fetcher: {
    get: <T = unknown>(url: string, params?: Record<string, any>, responseContentType?: ResponseContentType) => Promise<T>;
    post: <T = unknown, B = Record<string, any>>(url: string, body: B, requestContentType?: RequestContentType, responseContentType?: ResponseContentType) => Promise<T>;
};
//# sourceMappingURL=fetcher.d.ts.map