// 정제된 basemap API (GET/POST 구분 및 타입 매칭)
import { apiHelper } from "../utils/geonAPI";
const type = "smt";
// 동적 API 클라이언트 생성 함수
export function createGeonSmtClient(config = {}) {
    const { baseUrl, crtfckey } = config;
    const api = apiHelper({ type, baseUrl, crtfckey });
    return {
        // 예: commonCode 그대로 유지
        commonCode: {
            /** 공통 그룹 코드 조회 */
            getGroupCodes: api.get("/cmmn/code/group"),
            /** 공통 상세 코드 조회 */
            getDetailCodes: api.get("/cmmn/code/detail"),
        },
        // 레이어 공유 API
        layerShare: {
            /** 레이어 공유 등록 */
            insert: api.post("/layer/share/insert"),
            /** 레이어 공유 수정 */
            update: api.post("/layer/share/update"),
            /** 레이어 공유 삭제 */
            delete: api.post("/layer/share/delete"),
            /** 레이어 공유 조회 */
            select: api.get("/layer/share/select"),
            /** 사용자 ID로 공유된 레이어 조회 */
            selectByUserId: api.get("/layer/share/select/userId"),
        },
        // 레이어 그룹 정보 API
        layerGroupInfo: {
            /** 레이어 그룹 목록 조회 */
            list: api.get("/lyrgrp/info/list"),
            /** 레이어 그룹 등록 */
            insert: api.post("/lyrgrp/info/insert"),
            /** 레이어 그룹 수정 */
            update: api.post("/lyrgrp/info/update"),
            /** 레이어 그룹 삭제 */
            delete: api.post("/lyrgrp/info/delete"),
            /** 레이어 그룹 조회 */
            select: api.get("/lyrgrp/info/select"),
            /** 레이어 그룹 목록 엑셀 다운로드 */
            excel: api.get("/lyrgrp/info/excel"),
            /** 레이어 그룹 목록 조회(v2) */
            listV2: api.get("/lyrgrp/info/v2/list"),
        },
        // 레이어 API
        layer: {
            /** 레이어 목록 조회 */
            infoList: api.get("/layer/info/list"),
            /** 레이어 상세 조회 */
            infoSelect: api.get("/layer/info/select"),
        },
        // 주소 검색 API
        addr: {
            /** 주소 검색 */
            search: api.get("/addr/search"),
        },
        // 베이스맵 API
        basemap: {
            /** 베이스맵 수정 */
            update: api.post("/basemap/update"),
            /** 베이스맵 등록 */
            insert: api.post("/basemap/insert"),
            /** 베이스맵 삭제 */
            delete: api.post("/basemap/delete"),
            /** 베이스맵 조회 */
            select: api.get("/basemap/select"),
            /** 베이스맵 목록 */
            list: api.get("/basemap/list"),
        },
    };
}
export const defaultGeonSmtClient = createGeonSmtClient();
