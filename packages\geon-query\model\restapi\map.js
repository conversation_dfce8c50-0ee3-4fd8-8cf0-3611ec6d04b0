import { apiHelper } from "../utils/geonAPI";
const type = "map";
// 동적 API 클라이언트 생성 함수
export function createGeonMapClient(config = {}) {
    const { baseUrl, crtfckey } = config;
    const api = apiHelper({ type, baseUrl, crtfckey });
    return {
        // 레이어 API
        layer: {
            /** 레이어 컨텐츠 조회 */
            cnSelect: api.get("/layer/cn/select"),
        },
    };
}
export const defaultGeonMapClient = createGeonMapClient();
