/** @Todo : analysis-type 만들고 import, fetcher import */
import { BlobResponse } from "../utils/geonAPI";
import { LayerFileDownloadRequest } from "./type/analysis-type";
export interface AnalysisConfig {
    baseUrl?: string;
    crtfckey?: string;
    timeout?: number;
}
export type GeonAnalysisClient = ReturnType<typeof createGeonAnalysisClient>;
export declare function createGeonAnalysisClient(config?: AnalysisConfig): {
    fileDownload: {
        /** ### 레이어 파일 다운로드 (e.g. .csv, .zip, .geojson, .kml) */
        layerFileDownload: (params: LayerFileDownloadRequest) => Promise<BlobResponse>;
    };
};
//# sourceMappingURL=analysis.d.ts.map