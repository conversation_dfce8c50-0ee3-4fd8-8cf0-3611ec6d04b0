/**
 * OAuth 기반 도구 실행 핸들러
 * 
 * 모든 핸들러는 다음 패턴을 따릅니다:
 * 1. 입력 파라미터 검증
 * 2. OAuth 토큰 패스쓰루
 * 3. API 호출 및 응답 처리
 * 4. 표준화된 에러 처리
 */

import { CallToolResult } from '@modelcontextprotocol/sdk/types.js';
import { getToolDefinition } from '../tools/definitions.js';
import { extractAuthToken, extractUserIdFromToken, smartFetch } from '../utils/auth-fetch.js';
import { createGeonSmtClient } from '@geon-query/model/restapi/smt';
import { createGeonMapClient } from '@geon-query/model/restapi/map';
import { createGeonAddrgeoClient } from '@geon-query/model/restapi/addrgeo';

// OAuth 토큰 추출 함수는 auth-fetch.ts로 이동됨

/**
 * 표준화된 에러 응답 생성 (MCP 표준 준수)
 * Tool Execution Error로 처리 (Protocol Error가 아님)
 */
function createErrorResponse(error: Error | string): CallToolResult {
  const errorMessage = error instanceof Error ? error.message : error;
  console.error('Tool execution error:', errorMessage);

  const errorData = {
    error: true,
    message: errorMessage,
    timestamp: new Date().toISOString(),
  };

  return {
    content: [
      {
        type: 'text',
        text: `Error: ${errorMessage}`,
      },
    ],
    // MCP 표준: Structured Content로 에러 정보도 제공
    structuredContent: errorData,
    isError: true,
  };
}

/**
 * 표준화된 성공 응답 생성 (MCP 표준 준수)
 * Structured Content와 하위 호환성을 위한 Unstructured Content 모두 제공
 */
function createSuccessResponse(data: any): CallToolResult {
  return {
    content: [
      {
        type: 'text',
        text: JSON.stringify(data, null, 2),
      },
    ],
    // MCP 표준: Structured Content 제공
    structuredContent: data,
  };
}

// API 요청 헬퍼 함수는 auth-fetch.ts로 이동됨

/**
 * geon_get_layer_info_list 도구 실행 핸들러
 */
export async function executeGeonGetLayerInfoList(args: any, extra: any): Promise<CallToolResult> {
  try {
    // 입력 파라미터 검증 및 기본값 설정
    const {
      layerName = "",
      holdDataSeCode = "0"
    } = args || {};

    // 도구 정의에서 인증 요구사항 확인
    const toolDef = getToolDefinition('geon_get_layer_info_list');
    const requiresAuth = toolDef?.requiresAuth ?? false;

    // OAuth 토큰 추출
    const authToken = extractAuthToken(extra);

    // API 설정
    const baseUrl = process.env.GEON_API_BASE_URL || 'http://**************:14090';

    // 요청 파라미터 구성 (빈 값은 제외)
    const params = new URLSearchParams();

    // 빈 문자열이 아닌 경우에만 파라미터 추가
    if (layerName && layerName.trim() !== "") {
      params.set("searchTxt", layerName);
    }

    params.set("holdDataSeCode", holdDataSeCode);
    params.set("pageIndex", "1");
    params.set("pageSize", "15");

    // JWT 토큰에서 userId 추출하여 설정
    if (authToken) {
      const userId = extractUserIdFromToken(authToken);
      if (userId) {
        params.set("userId", userId);
      }
    }

    // 스마트 fetch를 사용한 API 호출
    const response = await smartFetch(
      `${baseUrl}/smt/layer/info/list?${params.toString()}`,
      requiresAuth,
      authToken,
      { method: 'GET' }
    );

    if (!response.ok) {
      const data = await response.json();
      console.error(`API request failed with status ${JSON.stringify(data)}`);
      throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('API 응답 데이터:', JSON.stringify(data, null, 2));

    return createSuccessResponse(data);

  } catch (error: any) {
    return createErrorResponse(error);
  }
}

// 레이어 분류 코드 상수
const LayerClassCode = {
  ANALYSIS: "MPD011",
  EXTERNAL: "MPD013",
} as const;

// 서비스 타입 정의
type ServiceType = "wms" | "wfs" | "wmts" | "group" | "cluster" | "heatmap" | "geoImage";

// 유틸리티 함수들
const getServerUrl = async (serviceType: ServiceType, baseUrl: string): Promise<string> => {
  const mapBaseUrl = `${baseUrl}/map/api/map/`;
  if (serviceType === "heatmap" || serviceType === "cluster") {
    return `${mapBaseUrl}wfs`;
  } else if (serviceType === "group") {
    return `${mapBaseUrl}wms`;
  }
  return `${mapBaseUrl}${serviceType}`;
};

const determineServiceType = (
  layerInfo: any,
  lyrClCode: string,
  lyrClSeCode: string,
  lyrTySeCode: string
): ServiceType => {
  const svcTySeCode = layerInfo.result.svcTySeCode;
  if (lyrClCode === "MPD011" && lyrClSeCode === "04") return "heatmap";
  if (lyrClCode === "MPD011" && lyrClSeCode === "06") return "cluster";
  if (lyrClCode === "MPD013" && lyrClSeCode === "06" && lyrTySeCode === "5") return "group";
  if (lyrTySeCode === "6") return "geoImage";
  return svcTySeCode === "M" ? "wms" : svcTySeCode === "T" ? "wmts" : "wfs";
};

const getGeometryType = (lyrTySeCode: string): string | undefined => {
  switch (lyrTySeCode) {
    case "1": return "point";
    case "2": return "line";
    case "3": return "polygon";
    case "4": return "geoTiff";
    default: return undefined;
  }
};

const createExternalApiLayerConfig = (layerInfo: any) => {
  const param = JSON.parse(layerInfo.result.mapUrlParamtr || "{}");
  const style = layerInfo.result.symbolCndCn ? JSON.parse(layerInfo.result.symbolCndCn) : undefined;
  return {
    layerConfig: {
      params: {
        ...param,
        projection: `EPSG:${layerInfo.result.cntmSeCode}`,
      },
    },
    style,
    layerInfo: {
      lyrId: layerInfo.result.lyrId,
      lyrNm: layerInfo.result.lyrNm,
      description: layerInfo.result.lyrDc,
    },
  };
};

const generateRandomStyle = (geometryType: string): any => {
  const randomColor = () => Math.floor(Math.random() * 255);
  const color = `rgb(${randomColor()}, ${randomColor()}, ${randomColor()})`;
  return {
    styleObject: [
      {
        seperatorFunc: "default",
        style: {
          geometryType: "free",
          image: geometryType === "point" ? {
            circle: {
              radius: 5,
              fill: { color },
              stroke: { color: "#000000", width: 1 },
            },
          } : {},
          fill: geometryType === "polygon" ? { color } : undefined,
          stroke: {
            color: geometryType === "line" ? color : "#000000",
            width: 1,
          },
        },
      },
    ],
    serviceType: "vector",
  };
};

/**
 * geon_get_layer_info_select 도구 실행 핸들러
 * 기존 getLayer 도구의 모든 로직을 포함한 완전한 레이어 설정 생성
 */
export async function executeGeonGetLayerInfoSelect(args: any, extra: any): Promise<CallToolResult> {
  try {
    const { lyrId } = args || {};

    if (!lyrId) {
      throw new Error('lyrId parameter is required');
    }

    const toolDef = getToolDefinition('geon_get_layer_info_select');
    const requiresAuth = toolDef?.requiresAuth ?? false;
    const authToken = extractAuthToken(extra);
    const baseUrl = process.env.GEON_API_BASE_URL || 'http://**************:14090';

    // JWT 토큰에서 userId 추출
    let userId: string | undefined;
    if (authToken) {
      userId = extractUserIdFromToken(authToken);
    }

    // 1. 레이어 기본 정보 조회
    const params = new URLSearchParams({ lyrId });
    if (userId) {
      params.set("sessionUserId", userId);
    }

    const response = await smartFetch(
      `${baseUrl}/smt/layer/info/select?${params.toString()}`,
      requiresAuth,
      authToken,
      { method: 'GET' }
    );

    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}`);
    }

    const layerInfo = await response.json();
    if (!layerInfo || !layerInfo.result) {
      return createErrorResponse(`${lyrId} < 레이어를 찾을 수 없습니다.`);
    }

    // 2. 레이어 컨텐츠 정보 조회
    const contentParams = new URLSearchParams({
      cntntsId: layerInfo.result.cntntsId || "",
    });

    const responseContent = await smartFetch(
      `${baseUrl}/map/layer/cn/select?${contentParams.toString()}`,
      requiresAuth,
      authToken,
      { method: 'GET' }
    );

    if (!responseContent.ok) {
      throw new Error(`API request failed with status ${responseContent.status}`);
    }

    const layerContent = await responseContent.json();
    if (!layerContent || !layerContent.result) {
      return createErrorResponse("레이어 컨텐츠 정보를 찾을 수 없습니다.");
    }

    const { lyrClCode, lyrClSeCode, lyrTySeCode } = layerInfo.result;

    // 3. 서비스 타입 결정
    const serviceType = determineServiceType(layerInfo, lyrClCode, lyrClSeCode, lyrTySeCode);
    const geometryType = getGeometryType(lyrTySeCode);

    // 4. 외부 API 레이어 처리 (MPD013, lyrClSeCode: 11)
    if (lyrClCode === LayerClassCode.EXTERNAL && lyrClSeCode === "11") {
      const externalConfig = createExternalApiLayerConfig(layerInfo);
      const result = {
        id: layerInfo.result.lyrId,
        name: layerInfo.result.lyrNm,
        type: "api",
        visible: true,
        zIndex: 0,
        info: externalConfig.layerInfo,
        ...externalConfig.layerConfig.params,
        style: externalConfig.style,
      };
      return createSuccessResponse(result);
    }

    // 5. 기본 레이어 설정
    let layerConfig: any = {
      id: layerInfo.result.lyrId,
      name: layerInfo.result.lyrNm,
      type: serviceType === "geoImage" ? "geoImage" : "geoserver",
      visible: layerInfo.result.onOffAt !== "N",
      zIndex: 0,
      server: await getServerUrl(serviceType, baseUrl),
      layer: `${layerContent.result.namespace}:${layerInfo.result.cntntsId}`,
      service: serviceType,
      bbox: false,
      method: serviceType === "wmts" ? "get" : "post",
      // RS 보안 정책: 클라이언트 토큰을 레이어 설정에 포함하지 않음
      projection: `EPSG:${layerInfo.result.cntmSeCode}`,
      geometryType,
      info: {
        lyrId: layerInfo.result.lyrId,
        lyrNm: layerInfo.result.lyrNm,
        description: layerInfo.result.lyrDc,
        metadata: {
          cntntsId: layerInfo.result.cntntsId,
          jobClCode: layerInfo.result.jobClCode,
          lyrClCode,
          lyrTySeCode,
          namespace: layerContent.result.namespace,
        },
      },
      namespace: layerContent.result.namespace,
      matrixSet: serviceType === "wmts" ? `EPSG:${layerInfo.result.cntmSeCode}` : undefined,
    };

    // 6. GeoImage 처리
    if (serviceType === "geoImage") {
      const geoImgParams = new URLSearchParams({
        fileName: layerInfo.result.mapUrl,
        lyrId: lyrId,
      });

      const geoImgResponse = await smartFetch(
        `${baseUrl}/smt/layer/geoImage?${geoImgParams.toString()}`,
        requiresAuth,
        authToken,
        { method: 'GET' }
      );

      const geoImgResult = await geoImgResponse.json();
      const format = layerInfo.result.mapUrl.split(".").pop()?.toLowerCase();
      const base64 = `data:image/${format};base64,${geoImgResult?.result?.base64}`;

      layerConfig = {
        ...layerConfig,
        type: "geoImage",
        url: base64,
        ...(layerInfo.result.mapUrlParamtr && JSON.parse(layerInfo.result.mapUrlParamtr)),
        opacity: layerInfo.result.symbolCndCn && JSON.parse(layerInfo.result.symbolCndCn)?.opacity,
      };
    }

    // 7. 스타일 처리
    let style = layerInfo.result.symbolCndCn ? JSON.parse(layerInfo.result.symbolCndCn) : undefined;
    if (style) {
      if (["vector", "cluster"].includes(style.serviceType)) {
        layerConfig.style = style.styleObject; // For vector/cluster, use styleObject directly
      } else if (style.serviceType === "image") {
        layerConfig.style = style.styleObject; // For WMS, SLD style
      } else if (style.serviceType === "heatmap") {
        layerConfig = { ...layerConfig, ...style.styleObject }; // Heatmap-specific props
      }
      if (style.opacity) layerConfig.opacity = style.opacity;
    } else if (serviceType === "wfs" && ["1", "2", "3"].includes(lyrTySeCode)) {
      layerConfig.style = generateRandomStyle(geometryType!); // Random style for WFS without style
    }

    // 8. 필터 처리
    if (
      lyrTySeCode !== "5" &&
      lyrTySeCode !== "4" &&
      lyrTySeCode !== "0" &&
      lyrTySeCode !== "6" &&
      !(lyrClCode === "MPD013" && lyrClSeCode === "11")
    ) {
      layerConfig.filter = layerInfo.result.flterCndCn;
      layerConfig.dynmFlterCndCn = layerInfo.result.dynmFlterCndCn;
    }

    return createSuccessResponse(layerConfig);

  } catch (error: any) {
    return createErrorResponse(`레이어 설정 생성 실패: ${error.message}`);
  }
}

/**
 * geon_search_address 도구 실행 핸들러
 */
export async function executeGeonSearchAddress(args: any, extra: any): Promise<CallToolResult> {
  try {
    const {
      keyword,
      showMultipleResults = false,
      targetSrid = 4326,
      countPerPage = 10,
      currentPage = 1
    } = args || {};

    if (!keyword || keyword.trim() === "") {
      throw new Error('keyword parameter is required');
    }

    const toolDef = getToolDefinition('geon_search_address');
    const requiresAuth = toolDef?.requiresAuth ?? false;
    const authToken = extractAuthToken(extra);
    const baseUrl = process.env.GEON_API_BASE_URL || 'http://**************:14090';

    const params = new URLSearchParams();
    params.set("keyword", keyword.trim());
    params.set("showMultipleResults", showMultipleResults.toString());
    params.set("targetSrid", targetSrid.toString());
    params.set("countPerPage", countPerPage.toString());
    params.set("currentPage", currentPage.toString());

    const response = await smartFetch(
      `${baseUrl}/smt/addr/search?${params.toString()}`,
      requiresAuth,
      authToken,
      { method: 'GET' }
    );

    if (!response.ok) {
      // 바디가 JSON이 아닐 수도 있어 안전 처리
      const raw = await response.text();
      let errJson: any;
      try { errJson = JSON.parse(raw); } catch { errJson = { _raw: raw }; }
      console.error(`API request failed: HTTP ${response.status}`, errJson);
      throw new Error(`HTTP ${response.status}`);
    }

    // 안전 파싱: JSON이 아니면 원문을 structuredContent._raw로 반환
    const raw = await response.text();
    let data: any;
    try {
      data = JSON.parse(raw);
    } catch {
      data = { _raw: raw };
    }
    console.log('API 응답 데이터:', JSON.stringify(data, null, 2));

    return createSuccessResponse(data);

  } catch (error: any) {
    return createErrorResponse(error?.message || String(error));
  }
}

/**
 * geon_search_coord 도구 실행 핸들러
 */
export async function executeGeonSearchCoord(args: any, extra: any): Promise<CallToolResult> {
  try {
    // 입력 파라미터 검증
    const { lat, lng } = args;

    if (!lat || !lng) {
      return createErrorResponse('위도(lat)와 경도(lng) 파라미터가 필요합니다.');
    }

    // OAuth 토큰 추출
    const token = extractAuthToken(extra);
    if (!token) {
      return createErrorResponse('OAuth 토큰이 필요합니다.');
    }

    // geon-query/model 클라이언트 생성
    const apiKey = 'UxizIdSqCePz93ViFt8ghZFFJuOzvUp0';
    const baseUrl = 'https://city.geon.kr/api/';

    const addressClient = createGeonAddrgeoClient({
      baseUrl,
      crtfckey: apiKey,
    });

    // 좌표 → 주소 변환 API 호출
    const data = await addressClient.address.coord({
      lat: lat.toString(),
      lng: lng.toString(),
    });

    // 응답 데이터 구조화
    const result = {
      summary: `좌표 검색이 완료되었습니다. 위도: ${lat}, 경도: ${lng}`,
      coordinate: { lat, lng },
      result: data,
      timestamp: new Date().toISOString()
    };

    return createSuccessResponse(result);

  } catch (error: any) {
    return createErrorResponse(error);
  }
}

/**
 * OAuth 기반 도구 실행 라우터
 */
export async function executeOAuthTool(
  toolName: string,
  args: any,
  extra: any
): Promise<CallToolResult> {
  switch (toolName) {
    case 'geon_get_layer_info_list':
      return await executeGeonGetLayerInfoList(args, extra);

    case 'geon_get_layer_info_select':
      return await executeGeonGetLayerInfoSelect(args, extra);

    case 'geon_search_address':
      return await executeGeonSearchAddress(args, extra);

    case 'geon_search_coord':
      return await executeGeonSearchCoord(args, extra);

    default:
      return createErrorResponse(`OAuth tool '${toolName}' not found`);
  }
}
