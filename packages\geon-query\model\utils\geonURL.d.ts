export declare const url: {
    readonly map_comment: "지도 요청지도 요청 기능 제공 합니다.";
    readonly map: {
        readonly baroemap_comment: "바로e맵 서비스";
        readonly baroemap: "/api/map/baroemap";
        readonly ngisair_comment: "정사영상 서비스";
        readonly ngisair: "/api/map/ngisair";
        readonly wfs_comment: "wfs 서비스 [POST]";
        readonly wfs: "/api/map/wfs";
        readonly wms_comment: "wms 서비스 [POST]";
        readonly wms: "/api/map/wms";
        readonly wmts_comment: "wmts 서비스";
        readonly wmts: "/api/map/wmts";
    };
    readonly vworld_comment: "지도 요청지도 요청 기능 제공 합니다.";
    readonly vworld: {
        readonly wfs_comment: "브이월드 서비스 (WFS)";
        readonly wfs: "/api/vworld/wfs";
        readonly wms_comment: "브이월드 서비스 (WMS)";
        readonly wms: "/api/vworld/wms";
    };
    readonly "{tileRow}_comment": "지도 요청지도 요청 기능 제공 합니다.";
    readonly "{tileRow}": {
        readonly "{tileCol}_comment": "브이월드 서비스 (WMTS)";
        readonly "{tileCol}": "/api/vworld/wmts/{layer}/{tileMatrix}/{tileRow}/{tileCol}.{tileType}";
    };
    readonly wmts_comment: "지도 요청지도 요청 기능 제공 합니다.";
    readonly wmts: {
        readonly capabilities_comment: "브이월드 서비스 (Capabilities)";
        readonly capabilities: "/api/vworld/wmts/capabilities";
    };
    readonly cn_comment: "컨텐츠관리컨텐츠 정보 관리 기능 제공 합니다.";
    readonly cn: {
        readonly delete_comment: "컨텐츠 삭제";
        readonly delete: "/layer/cn/delete";
        readonly list_comment: "컨텐츠 목록조회";
        readonly list: "/layer/cn/list";
        readonly select_comment: "컨텐츠 상세조회";
        readonly select: "/layer/cn/select";
        readonly update_comment: "컨텐츠 수정";
        readonly update: "/layer/cn/update";
    };
    readonly find_comment: "컨텐츠관리컨텐츠 정보 관리 기능 제공 합니다.";
    readonly find: {
        readonly fromEmdCode_comment: "컨텐츠검색(법정동선택)";
        readonly fromEmdCode: "/layer/cn/find/fromEmdCode";
        readonly fromGeometry_comment: "컨텐츠검색(영역선택)";
        readonly fromGeometry: "/layer/cn/find/fromGeometry";
    };
    readonly _comment: "테이블 레이어 정보 관리테이블 레이어 정보 관리 기능을 제공합니다.";
    readonly "": {
        readonly storage_comment: "저장소 삭제(관리자 시스템 전용)";
        readonly storage: "/storage";
        readonly table_comment: "테이블 레이어 목록 조회";
        readonly table: "/table";
    };
    readonly address_comment: "위치검색위치검색";
    readonly address: {
        readonly bld_comment: "건물명 검색";
        readonly bld: "/address/bld";
        readonly coord_comment: "경위도 좌표 위치검색";
        readonly coord: "/address/coord";
        readonly int_comment: "통합 위치검색";
        readonly int: "/address/int";
        readonly jibun_comment: "지번 위치검색";
        readonly jibun: "/address/jibun";
        readonly pnu_comment: "PNU 위치검색";
        readonly pnu: "/address/pnu";
        readonly poi_comment: "POI 검색";
        readonly poi: "/address/poi";
        readonly road_comment: "도로명주소 위치검색";
        readonly road: "/address/road";
    };
    readonly jibun_comment: "위치검색위치검색";
    readonly jibun: {
        readonly list_comment: "지번 위치검색(다건)";
        readonly list: "/address/jibun/list";
    };
    readonly road_comment: "위치검색위치검색";
    readonly road: {
        readonly link_comment: "도로명주소API 검색";
        readonly link: "/address/road/link";
    };
    readonly refine_comment: "주소정제주소정제";
    readonly refine: {
        readonly file_comment: "주소정제(파일)";
        readonly file: "/refine/file";
        readonly single_comment: "주소정제(단일)";
        readonly single: "/refine/single";
        readonly stream_comment: "주소정제(Stream)";
        readonly stream: "/refine/stream";
    };
    readonly geofile_comment: "지오코딩 결과 파일 관리지오코딩 결과 파일 관리 기능 제공 합니다.";
    readonly geofile: {
        readonly delete_comment: "지오코딩 결과 파일 삭제";
        readonly delete: "/geofile/delete";
        readonly insert_comment: "지오코딩 결과 파일 등록";
        readonly insert: "/geofile/insert";
        readonly list_comment: "지오코딩 결과 파일 7일전 목록 조회";
        readonly list: "/geofile/list";
        readonly select_comment: "지오코딩 결과 파일 경로 조회";
        readonly select: "/geofile/select";
        readonly update_comment: "지오코딩 결과 파일 수정";
        readonly update: "/geofile/update";
    };
    readonly select_comment: "지오코딩 결과 파일 관리지오코딩 결과 파일 관리 기능 제공 합니다.";
    readonly select: {
        readonly userId_comment: "사용자가 공유받은 레이어의 공유 정보 조회";
        readonly userId: "/layer/share/select/userId";
        readonly "image-contents_comment": "웹앱 템플릿 이미지 컨텐츠 조회(스토리/탭 테마)";
        readonly "image-contents": "/webapp/tmplat/select/image-contents";
        readonly detail_comment: "지오코딩 결과 상세 조회";
        readonly detail: "/geofile/select/detail";
    };
    readonly geoco_comment: "지오코딩지오코딩";
    readonly geoco: {
        readonly file_comment: "지오코딩(파일)";
        readonly file: "/geoco/file";
        readonly stream_comment: "지오코딩";
        readonly stream: "/geoco/stream";
    };
    readonly administ_comment: "행정구역 검색행정구역 검색";
    readonly administ: {
        readonly ctpv_comment: "시도 검색";
        readonly ctpv: "/administ/ctpv";
        readonly emd_comment: "읍면동 검색";
        readonly emd: "/administ/emd";
        readonly li_comment: "리 검색";
        readonly li: "/administ/li";
        readonly sgg_comment: "시군구 검색";
        readonly sgg: "/administ/sgg";
    };
    readonly ctpv_comment: "행정구역 검색행정구역 검색";
    readonly ctpv: {
        readonly list_comment: "시도 리스트 검색";
        readonly list: "/administ/ctpv/list";
    };
    readonly emd_comment: "행정구역 검색행정구역 검색";
    readonly emd: {
        readonly list_comment: "읍면동 리스트 검색";
        readonly list: "/administ/emd/list";
    };
    readonly li_comment: "행정구역 검색행정구역 검색";
    readonly li: {
        readonly list_comment: "리 리스트 검색";
        readonly list: "/administ/li/list";
    };
    readonly sgg_comment: "행정구역 검색행정구역 검색";
    readonly sgg: {
        readonly list_comment: "시군구 리스트 검색";
        readonly list: "/administ/sgg/list";
    };
    readonly "geojson-file-to-shp_comment": "공간정보 변환공간정보 확장자 변환";
    readonly "geojson-file-to-shp": {
        readonly download_comment: "GeoJson 파일을 shp 파일로 변환";
        readonly download: "/convert/geojson-file-to-shp/download";
    };
    readonly "geojson-text-to-shp_comment": "공간정보 변환공간정보 확장자 변환";
    readonly "geojson-text-to-shp": {
        readonly download_comment: "GeoJson 텍스트를 shp 파일로 변환";
        readonly download: "/convert/geojson-text-to-shp/download";
    };
    readonly pttrn_comment: "도형분석-공간패턴 분석공간패턴 분석";
    readonly pttrn: {
        readonly density_comment: "밀도 분석";
        readonly density: "/anals/pttrn/density";
        readonly hotspot_comment: "핫 스팟 분석(V2)";
        readonly hotspot: "/anals/pttrn/hotspot";
        readonly interpolatePoints_comment: "포인트 내삽 찾기 분석";
        readonly interpolatePoints: "/anals/pttrn/interpolatePoints";
    };
    readonly proximity_comment: "도형분석-근접도 분석근접도 분석";
    readonly proximity: {
        readonly buffer_comment: "버퍼 분석";
        readonly buffer: "/anals/proximity/buffer";
        readonly connectDestination_comment: "출발지와 목적지 연결 분석";
        readonly connectDestination: "/anals/proximity/connectDestination";
        readonly drivingArea_comment: "운전시간 영역 생성 분석";
        readonly drivingArea: "/anals/proximity/drivingArea";
        readonly findNearestPoint_comment: "최근접 위치찾기 분석";
        readonly findNearestPoint: "/anals/proximity/findNearestPoint";
        readonly findPath_comment: "경로계획 분석";
        readonly findPath: "/anals/proximity/findPath";
    };
    readonly manage_comment: "도형분석-데이터 관리 분석데이터 관리 분석";
    readonly manage: {
        readonly ar_comment: "객체별 면적 계산";
        readonly ar: "/anals/manage/ar";
        readonly clustering_comment: "클러스터링";
        readonly clustering: "/anals/manage/clustering";
        readonly dsslve_comment: "경계디졸브";
        readonly dsslve: "/anals/manage/dsslve";
        readonly dvsion_comment: "공간 분할 생성";
        readonly dvsion: "/anals/manage/dvsion";
        readonly lt_comment: "객체별 길이 계산";
        readonly lt: "/anals/manage/lt";
        readonly merge_comment: "병합(Merge)";
        readonly merge: "/anals/manage/merge";
    };
    readonly extrc_comment: "도형분석-데이터 관리 분석데이터 관리 분석";
    readonly extrc: {
        readonly file_comment: "데이터 추출(파일)";
        readonly file: "/anals/manage/extrc/file";
        readonly stream_comment: "데이터 추출(스트림)";
        readonly stream: "/anals/manage/extrc/stream";
    };
    readonly ovrlay_comment: "도형분석-데이터 관리 분석데이터 관리 분석";
    readonly ovrlay: {
        readonly erase_comment: "지우기(Erase)";
        readonly erase: "/anals/manage/ovrlay/erase";
        readonly intsct_comment: "교차(Intersect)";
        readonly intsct: "/anals/manage/ovrlay/intsct";
        readonly union_comment: "결합(Union)";
        readonly union: "/anals/manage/ovrlay/union";
    };
    readonly intsct_comment: "도형분석-데이터 관리 분석데이터 관리 분석";
    readonly intsct: {
        readonly coverage_comment: "레스터 레이어 교차(Intersect)";
        readonly coverage: "/anals/manage/ovrlay/intsct/coverage";
    };
    readonly sumry_comment: "도형분석-데이터 요약 분석데이터 요약 분석";
    readonly sumry: {
        readonly ag_comment: "포인트집계 분석";
        readonly ag: "/anals/sumry/ag";
        readonly center_comment: "공간분포 패턴";
        readonly center: "/anals/sumry/center";
        readonly join_comment: "공간 조인";
        readonly join: "/anals/sumry/join";
        readonly nrby_comment: "주변 집계";
        readonly nrby: "/anals/sumry/nrby";
        readonly range_comment: "영역 내 집계";
        readonly range: "/anals/sumry/range";
    };
    readonly lc_comment: "도형분석-위치찾기 분석위치찾기 분석";
    readonly lc: {
        readonly searchCenter_comment: "중심 찾기";
        readonly searchCenter: "/anals/lc/searchCenter";
        readonly searchLegacy_comment: "공간 조건 검색";
        readonly searchLegacy: "/anals/lc/searchLegacy";
        readonly searchNew_comment: "공간 조건 추출";
        readonly searchNew: "/anals/lc/searchNew";
    };
    readonly ntice_comment: "작업알림작업알림";
    readonly ntice: {
        readonly detail_comment: "작업 상세조회";
        readonly detail: "/ntice/detail";
        readonly seedState_comment: "시드 상태 조회";
        readonly seedState: "/ntice/seedState";
    };
    readonly file_comment: "파일 다운로드레이어 또는 속성 정보를 파일로 다운로드하는 기능을 제공 합니다.";
    readonly file: {
        readonly download_comment: "레이어 파일 다운로드";
        readonly download: "/layer/file/download";
        readonly donwload_comment: "이미지 레이어 다운로드";
        readonly donwload: "/image/layer/file/donwload";
    };
    readonly geotiff_comment: "파일 다운로드레이어 또는 속성 정보를 파일로 다운로드하는 기능을 제공 합니다.";
    readonly geotiff: {
        readonly download_comment: "GeoTIFF 레이어 다운로드";
        readonly download: "/image/layer/geotiff/download";
    };
    readonly dxf_comment: "공간 도형 정보 업로드공간 도형 정보 업로드 입니다";
    readonly dxf: {
        readonly file_comment: "Dxf 파일 서비스 발행";
        readonly file: "/upload/spatial/dxf/file";
        readonly stream_comment: "Dxf 파일 업로드";
        readonly stream: "/upload/spatial/dxf/stream";
    };
    readonly coord_comment: "좌표변환좌표변환";
    readonly coord: {
        readonly single_comment: "단일 좌표 및 도분초 변환";
        readonly single: "/coord/single";
        readonly stream_comment: "파일 좌표변환";
        readonly stream: "/coord/stream";
        readonly text_comment: "피처 문자열 좌표변환";
        readonly text: "/coord/text";
    };
    readonly db_comment: "공간 도형 정보 업로드공간 도형 정보 업로드 입니다";
    readonly db: {
        readonly republish_comment: "DB Table 서비스 재발행";
        readonly republish: "/upload/spatial/db/republish";
        readonly table_comment: "DB Table 서비스 발행";
        readonly table: "/upload/spatial/db/table";
    };
    readonly geojson_comment: "공간 도형 정보 업로드공간 도형 정보 업로드 입니다";
    readonly geojson: {
        readonly file_comment: "GeoJSON 파일 서비스 발행";
        readonly file: "/upload/spatial/geojson/file";
        readonly stream_comment: "GeoJSON 파일 업로드";
        readonly stream: "/upload/spatial/geojson/stream";
        readonly text_comment: "GeoJSON 텍스트 서비스 발행";
        readonly text: "/upload/spatial/geojson/text";
    };
    readonly netcdf_comment: "공간 도형 정보 업로드공간 도형 정보 업로드 입니다";
    readonly netcdf: {
        readonly file_comment: "NetCDF 파일 서비스 발행";
        readonly file: "/upload/spatial/netcdf/file";
        readonly stream_comment: "NetCdf 파일 업로드";
        readonly stream: "/upload/spatial/netcdf/stream";
    };
    readonly shp_comment: "공간 도형 정보 업로드공간 도형 정보 업로드 입니다";
    readonly shp: {
        readonly file_comment: "SHP 파일 서비스 발행";
        readonly file: "/upload/spatial/shp/file";
        readonly stream_comment: "SHP 파일 업로드";
        readonly stream: "/upload/spatial/shp/stream";
    };
    readonly tif_comment: "공간 도형 정보 업로드공간 도형 정보 업로드 입니다";
    readonly tif: {
        readonly file_comment: "Tif 파일 서비스 발행";
        readonly file: "/upload/spatial/tif/file";
        readonly stream_comment: "Tif 파일 업로드";
        readonly stream: "/upload/spatial/tif/stream";
    };
    readonly image_comment: "사용자 이미지 관리사용자 이미지 파일을 등록/수정/삭제/조회";
    readonly image: {
        readonly delete_comment: "사용자 이미지 삭제";
        readonly delete: "/image/delete";
        readonly insert_comment: "사용자 이미지 등록";
        readonly insert: "/image/insert";
        readonly list_comment: "사용자 이미지 목록조회";
        readonly list: "/image/list";
        readonly select_comment: "사용자 이미지 상세조회";
        readonly select: "/image/select";
        readonly update_comment: "사용자 이미지 수정";
        readonly update: "/image/update";
    };
    readonly sld_comment: "SLD 관리지오서버에 저장된 SLD 정보를 관리합니다.";
    readonly sld: {
        readonly stream_comment: "SLD 추가(스트림)";
        readonly stream: "/sld/stream";
    };
    readonly code_comment: "공통 코드 조회공통 코드 조회 기능 제공 합니다.";
    readonly code: {
        readonly detail_comment: "공통 상세 코드 조회";
        readonly detail: "/cmmn/code/detail";
        readonly group_comment: "공통 그룹 코드 조회";
        readonly group: "/cmmn/code/group";
    };
    readonly instt_comment: "기관 정보기관 정보 관련 기능을 제공합니다.";
    readonly instt: {
        readonly id_comment: "기관 기본 정보 조회";
        readonly id: "/instt/id";
    };
    readonly share_comment: "웹앱 템플릿 관리웹앱 템플릿 관련 기능을 제공합니다.";
    readonly share: {
        readonly delete_comment: "웹앱 템플릿 부분 공유 삭제";
        readonly delete: "/webapp/tmplat/share/delete";
        readonly insert_comment: "웹앱 템플릿 공유 등록 및 수정";
        readonly insert: "/webapp/tmplat/share/insert";
        readonly select_comment: "웹앱 템플릿 공유 조회";
        readonly select: "/webapp/tmplat/share/select";
        readonly update_comment: "웹맵 공유 수정";
        readonly update: "/webmap/share/update";
        readonly list_comment: "웹앱 템플릿 공유 목록 조회";
        readonly list: "/webapp/tmplat/share/list";
    };
    readonly info_comment: "웹맵 관리웹맵 관련 기능을 제공합니다.";
    readonly info: {
        readonly delete_comment: "웹맵 삭제";
        readonly delete: "/webmap/info/delete";
        readonly excel_comment: "레이어 그룹 목록 엑셀 다운로드(관리자 시스템 전용)";
        readonly excel: "/lyrgrp/info/excel";
        readonly insert_comment: "웹맵 등록";
        readonly insert: "/webmap/info/insert";
        readonly list_comment: "웹맵 목록 조회";
        readonly list: "/webmap/info/list";
        readonly select_comment: "웹맵 상세 조회";
        readonly select: "/webmap/info/select";
        readonly update_comment: "웹맵 수정";
        readonly update: "/webmap/info/update";
        readonly auth_comment: "웹맵 권한 체크";
        readonly auth: "/webmap/info/auth";
    };
    readonly v2_comment: "레이어 그룹 정보레이어 그룹 관련 기능을 제공합니다.";
    readonly v2: {
        readonly list_comment: "레이어 그룹 목록 조회(관리자)";
        readonly list: "/lyrgrp/info/v2/list";
    };
    readonly layer_comment: "TOC 레이어 설정TOC 레이어 설정 기능 제공 합니다.";
    readonly layer: {
        readonly attributes_comment: "레이어 속성 데이터 목록 조회";
        readonly attributes: "/layer/attributes";
        readonly delete_comment: "TOC 레이어설정 삭제";
        readonly delete: "/toc/layer/delete";
        readonly insert_comment: "TOC 레이어설정 등록";
        readonly insert: "/toc/layer/insert";
        readonly select_comment: "TOC 레이어설정  조회";
        readonly select: "/toc/layer/select";
        readonly update_comment: "TOC 레이어설정 수정";
        readonly update: "/toc/layer/update";
    };
    readonly attributes_comment: "레이어 속성 정보 관리레이어 속성 정보 관리 기능을 제공 합니다.";
    readonly attributes: {
        readonly aggregate_comment: "레이어 속성 데이터 집계";
        readonly aggregate: "/layer/attributes/aggregate";
        readonly bbox_comment: "레이어 BBOX 조회";
        readonly bbox: "/layer/attributes/bbox";
        readonly count_comment: "레이어 속성 데이터 개수 조회";
        readonly count: "/layer/attributes/count";
        readonly select_comment: "레이어 속성 데이터 목록 조회 (POST)";
        readonly select: "/layer/attributes/select";
    };
    readonly aggregate_comment: "레이어 속성 정보 관리레이어 속성 정보 관리 기능을 제공 합니다.";
    readonly aggregate: {
        readonly select_comment: "레이어 속성 데이터 집계 (POST)";
        readonly select: "/layer/attributes/aggregate/select";
    };
    readonly bbox_comment: "레이어 속성 정보 관리레이어 속성 정보 관리 기능을 제공 합니다.";
    readonly bbox: {
        readonly select_comment: "레이어 BBOX 조회 (POST)";
        readonly select: "/layer/attributes/bbox/select";
    };
    readonly count_comment: "레이어 컬럼 정보 관리레이어 컬럼 정보 관리 기능을 제공합니다.";
    readonly count: {
        readonly select_comment: "레이어 컬럼 유일값 갯수 조회 (POST)";
        readonly select: "/layer/column/unique/count/select";
    };
    readonly flter_comment: "레이어 속성필터 설정레이어 속성필터 설정 기능 제공 합니다.";
    readonly flter: {
        readonly delete_comment: "레이어 속성필터 삭제";
        readonly delete: "/layer/flter/delete";
        readonly insert_comment: "레이어 속성필터 등록 및 수정";
        readonly insert: "/layer/flter/insert";
        readonly select_comment: "레이어 속성필터 조회";
        readonly select: "/layer/flter/select";
    };
    readonly style_comment: "레이어 스타일 설정레이어 스타일 설정 기능 제공 합니다.";
    readonly style: {
        readonly delete_comment: "레이어 스타일 삭제";
        readonly delete: "/layer/style/delete";
        readonly insert_comment: "레이어 스타일 등록";
        readonly insert: "/layer/style/insert";
        readonly select_comment: "레이어 스타일 조회";
        readonly select: "/layer/style/select";
        readonly update_comment: "레이어 스타일 수정";
        readonly update: "/layer/style/update";
    };
    readonly symbol_comment: "레이어 심볼 설정레이어 심볼 설정 기능 제공 합니다.";
    readonly symbol: {
        readonly delete_comment: "이미지 심볼 삭제";
        readonly delete: "/layer/symbol/delete";
        readonly insert_comment: "이미지 심볼 등록";
        readonly insert: "/layer/symbol/insert";
        readonly select_comment: "이미지 심볼 조회";
        readonly select: "/layer/symbol/select";
        readonly update_comment: "이미지 심볼 수정";
        readonly update: "/layer/symbol/update";
    };
    readonly thumbnail_comment: "웹앱 템플릿의 썸네일 관리웹앱 템플릿의 썸네일을 등록/수정/삭제";
    readonly thumbnail: {
        readonly delete_comment: "웹앱 썸네일 삭제";
        readonly delete: "/webapp/tmplat/thumbnail/delete";
        readonly insert_comment: "웹맵 썸네일 등록";
        readonly insert: "/webmap/thumbnail/insert";
        readonly select_comment: "웹맵 썸네일 조회";
        readonly select: "/webmap/thumbnail/select";
        readonly update_comment: "웹앱 썸네일 등록 및 수정";
        readonly update: "/webapp/tmplat/thumbnail/update";
    };
    readonly update_comment: "웹맵 관리웹맵 관련 기능을 제공합니다.";
    readonly update: {
        readonly body_comment: "웹맵 썸네일 수정(RequestBody)";
        readonly body: "/webmap/thumbnail/update/body";
        readonly type_comment: "레이어 컬럼 타입 수정";
        readonly type: "/layer/column/update/type";
        readonly code_comment: "사용공유 구분코드 수정";
        readonly code: "/webmap/info/update/code";
    };
    readonly column_comment: "레이어 컬럼 정보 관리레이어 컬럼 정보 관리 기능을 제공합니다.";
    readonly column: {
        readonly create_comment: "레이어 컬럼 생성";
        readonly create: "/layer/column/create";
        readonly insert_comment: "레이어 컬럼 정보 등록 및 수정";
        readonly insert: "/layer/column/insert";
        readonly range_comment: "레이어 컬럼 범위 조회";
        readonly range: "/layer/column/range";
        readonly select_comment: "레이어 컬럼 정보 조회";
        readonly select: "/layer/column/select";
        readonly unique_comment: "레이어 컬럼 유일값 조회";
        readonly unique: "/layer/column/unique";
    };
    readonly unique_comment: "레이어 컬럼 정보 관리레이어 컬럼 정보 관리 기능을 제공합니다.";
    readonly unique: {
        readonly count_comment: "레이어 컬럼 유일값 갯수 조회";
        readonly count: "/layer/column/unique/count";
        readonly select_comment: "레이어 컬럼 유일값 조회 (POST)";
        readonly select: "/layer/column/unique/select";
    };
    readonly popup_comment: "레이어 팝업 설정레이어 팝업 설정 기능 제공 합니다.";
    readonly popup: {
        readonly delete_comment: "레이어 팝업 설정 삭제";
        readonly delete: "/layer/popup/delete";
        readonly insert_comment: "레이어 팝업 설정 등록 및 수정";
        readonly insert: "/layer/popup/insert";
        readonly select_comment: "레이어 팝업 설정 조회";
        readonly select: "/layer/popup/select";
    };
    readonly basemap_comment: "베이스맵 관리베이스맵 정보 관리 기능 제공 합니다.";
    readonly basemap: {
        readonly delete_comment: "베이스맵 삭제";
        readonly delete: "/basemap/delete";
        readonly insert_comment: "베이스맵 추가";
        readonly insert: "/basemap/insert";
        readonly list_comment: "베이스맵 목록";
        readonly list: "/basemap/list";
        readonly select_comment: "베이스맵 조회";
        readonly select: "/basemap/select";
        readonly update_comment: "베이스맵 수정";
        readonly update: "/basemap/update";
    };
    readonly users_comment: "사용자 관리사용자 관련 기능을 제공합니다.";
    readonly users: {
        readonly id_comment: "사용자 조회";
        readonly id: "/users/id";
    };
    readonly bkmk_comment: "사용자 북마크 관리사용자 북마크 관리 기능 제공 합니다.";
    readonly bkmk: {
        readonly delete_comment: "사용자 북마크 삭제";
        readonly delete: "/bkmk/delete";
        readonly insert_comment: "사용자 북마크 등록";
        readonly insert: "/bkmk/insert";
        readonly list_comment: "사용자 북마크 목록 조회";
        readonly list: "/bkmk/list";
        readonly select_comment: "사용자 북마크 조회";
        readonly select: "/bkmk/select";
        readonly update_comment: "사용자 북마크 수정";
        readonly update: "/bkmk/update";
    };
    readonly opertntcn_comment: "사용자 지도 작업 알림 관리사용자 지도 작업 알림 관리 기능 제공 합니다.";
    readonly opertntcn: {
        readonly delete_comment: "사용자 지도 작업 알림 삭제";
        readonly delete: "/usermap/opertntcn/delete";
        readonly insert_comment: "사용자 지도 작업 알림 등록";
        readonly insert: "/usermap/opertntcn/insert";
        readonly list_comment: "사용자 지도 작업 알림 목록 조회";
        readonly list: "/usermap/opertntcn/list";
        readonly queue_comment: "사용자 지도 작업 알림 대기열 목록 조회";
        readonly queue: "/usermap/opertntcn/queue";
        readonly select_comment: "사용자 지도 작업 알림 조회";
        readonly select: "/usermap/opertntcn/select";
        readonly stats_comment: "사용자 지도 작업 알림 상태 변경";
        readonly stats: "/usermap/opertntcn/stats";
        readonly update_comment: "사용자 지도 작업 알림 수정";
        readonly update: "/usermap/opertntcn/update";
    };
    readonly stats_comment: "사용자 지도 작업 알림 관리사용자 지도 작업 알림 관리 기능 제공 합니다.";
    readonly stats: {
        readonly geocoding_comment: "사용자 지도 작업 알림 지오코딩 상태 변경";
        readonly geocoding: "/usermap/opertntcn/stats/geocoding";
    };
    readonly ownership_comment: "ApiBuildingRegistryController건축물 대장 정보 조회";
    readonly ownership: {
        readonly area_comment: "전유공유면적 조회";
        readonly area: "/api/registry/ownership/area";
    };
    readonly upload_comment: "업로드 이미지 레이어 정보 관리레이어 정보 관리 기능 제공 합니다.";
    readonly upload: {
        readonly image_comment: "이미지 레이어 정보 조회";
        readonly image: "/upload/image";
    };
    readonly url_comment: "웹 레이어 서비스웹 레이어에 대한 레이어 서비스 등록/수정/삭제/조회";
    readonly url: {
        readonly delete_comment: "웹레이어 삭제";
        readonly delete: "/layer/url/delete";
        readonly insert_comment: "웹레이어 등록";
        readonly insert: "/layer/url/insert";
        readonly list_comment: "웹레이어 목록조회";
        readonly list: "/layer/url/list";
        readonly select_comment: "웹레이어 상세조회";
        readonly select: "/layer/url/select";
        readonly update_comment: "웹레이어 수정";
        readonly update: "/layer/url/update";
    };
    readonly auth_comment: "웹앱 템플릿 관리웹앱 템플릿 관련 기능을 제공합니다.";
    readonly auth: {
        readonly fullshare_comment: "웹앱 템플릿 전체 공유 권한 체크";
        readonly fullshare: "/webapp/tmplat/auth/fullshare";
    };
    readonly insert_comment: "웹앱 템플릿 관리웹앱 템플릿 관련 기능을 제공합니다.";
    readonly insert: {
        readonly process_comment: "웹맵, 웹맵 레이어그룹, 웹맵 레이어설정, 웹맵 공유 일괄 저장";
        readonly process: "/webmap/info/insert/process";
        readonly body_comment: "웹맵 썸네일 등록(RequestBody)";
        readonly body: "/webmap/thumbnail/insert/body";
        readonly "image-contents_comment": "웹앱 템플릿 이미지 컨텐츠 등록(스토리/탭 테마)";
        readonly "image-contents": "/webapp/tmplat/insert/image-contents";
    };
    readonly allShare_comment: "웹앱 템플릿 관리웹앱 템플릿 관련 기능을 제공합니다.";
    readonly allShare: {
        readonly delete_comment: "웹앱 전체 공유 삭제";
        readonly delete: "/webapp/tmplat/allShare/delete";
        readonly insert_comment: "웹앱 전체 공유(개선 필요)";
        readonly insert: "/webapp/tmplat/allShare/insert";
    };
    readonly tmplat_comment: "웹앱 템플릿 관리웹앱 템플릿 관련 기능을 제공합니다.";
    readonly tmplat: {
        readonly auth_comment: "웹앱 템플릿 권한 체크";
        readonly auth: "/webapp/tmplat/auth";
        readonly delete_comment: "웹앱 템플릿 삭제";
        readonly delete: "/webapp/tmplat/delete";
        readonly insert_comment: "웹앱 템플릿 등록 및 수정";
        readonly insert: "/webapp/tmplat/insert";
        readonly list_comment: "웹앱 템플릿 목록 조회";
        readonly list: "/webapp/tmplat/list";
        readonly select_comment: "웹앱 템플릿 상세 조회";
        readonly select: "/webapp/tmplat/select";
    };
    readonly storage_comment: "저장소 정보 관리저장소 정보 관리 기능을 제공합니다.";
    readonly storage: {
        readonly activation_comment: "저장소 활성화 여부 수정(관리자 시스템 전용)";
        readonly activation: "/storage/activation";
        readonly detail_comment: "저장소 상세 조회(관리자 시스템 전용)";
        readonly detail: "/storage/detail";
    };
    readonly table_comment: "테이블 레이어 정보 관리테이블 레이어 정보 관리 기능을 제공합니다.";
    readonly table: {
        readonly publish_comment: "테이블 레이어 발행(관리자 시스템 전용)";
        readonly publish: "/table/publish";
        readonly republish_comment: "테이블 레이어 재발행(관리자 시스템 전용)";
        readonly republish: "/table/republish";
    };
    readonly group_comment: "TOC 그룹 설정TOC 그룹 설정 기능 제공 합니다.";
    readonly group: {
        readonly delete_comment: "TOC 그룹삭제";
        readonly delete: "/toc/group/delete";
        readonly insert_comment: "TOC 그룹등록";
        readonly insert: "/toc/group/insert";
        readonly select_comment: "TOC 그룹조회";
        readonly select: "/toc/group/select";
        readonly update_comment: "TOC 그룹수정";
        readonly update: "/toc/group/update";
    };
    readonly lry_comment: "TOC 그룹 설정TOC 그룹 설정 기능 제공 합니다.";
    readonly lry: {
        readonly id_comment: "레이어 그룹 ID 생성";
        readonly id: "/toc/group/lry/id";
    };
    readonly heading_comment: "ApiBuildController건물 조회(일필지)";
    readonly heading: {
        readonly floor_comment: "(Open-API) 건물층수 조회";
        readonly floor: "/api/builds/build/heading/floor";
        readonly ho_comment: "(Open-API) 건물호수 조회";
        readonly ho: "/api/builds/build/heading/ho";
    };
    readonly build_comment: "ApiBuildController건물 조회(일필지)";
    readonly build: {
        readonly register_comment: "(Open-API) 대지권 등록 목록 조회";
        readonly register: "/api/builds/build/register";
        readonly serial_comment: "(Open-API) 건물일련번호 조회";
        readonly serial: "/api/builds/build/serial";
    };
    readonly registry_comment: "ApiBuildingRegistryController건축물 대장 정보 조회";
    readonly registry: {
        readonly floor_comment: "층별개요 조회";
        readonly floor: "/api/registry/floor";
        readonly headings_comment: "표제부 조회";
        readonly headings: "/api/registry/headings";
        readonly ownership_comment: "전유부 조회";
        readonly ownership: "/api/registry/ownership";
    };
    readonly general_comment: "ApiBuildingRegistryController건축물 대장 정보 조회";
    readonly general: {
        readonly headings_comment: "총괄표제부 조회";
        readonly headings: "/api/registry/general/headings";
    };
    readonly land_comment: "ApiLandUsePlanController토지이용계획 조회(일필지)";
    readonly land: {
        readonly basic_comment: "(Open-API) 토지임야 목록 조회";
        readonly basic: "/api/land/basic";
        readonly characteristics_comment: "(Open-API) 토지특성 속성 조회";
        readonly characteristics: "/api/land/characteristics";
        readonly history_comment: "(Open-API) 토지이동이력 속성 조회";
        readonly history: "/api/land/history";
        readonly ownership_comment: "(Open-API) 토지소유정보 속성 조회";
        readonly ownership: "/api/land/ownership";
        readonly useplan_comment: "(Open-API) 토지이용계획 속성 조회";
        readonly useplan: "/api/land/useplan";
    };
    readonly parcel_comment: "ApiOnePcAllInqireController일필지 종합 정보";
    readonly parcel: {
        readonly all_comment: "(Open-API) 일필지 종합 정보 조회";
        readonly all: "/api/parcel/all";
    };
    readonly house_comment: "ApiPriceController가격 정보 조회(일필지)";
    readonly house: {
        readonly apt_comment: "(V-World) 공동주택가격 속성 조회";
        readonly apt: "/api/price/house/apt";
        readonly ind_comment: "(V-World) 개별주택가격 속성 조회";
        readonly ind: "/api/price/house/ind";
    };
    readonly price_comment: "ApiPriceController가격 정보 조회(일필지)";
    readonly price: {
        readonly pclnd_comment: "(V-World) 개별공시지가 속성 조회";
        readonly pclnd: "/api/price/pclnd";
    };
    readonly search_comment: "LandBundleController토지묶음";
    readonly search: {
        readonly address_comment: "";
        readonly address: "/api/landbundle/search/address";
    };
};
export type UrlType = typeof url;
export declare const url_comment: {
    "/api/map/baroemap": string;
    "/api/map/ngisair": string;
    "/api/map/wfs": string;
    "/api/map/wms": string;
    "/api/map/wmts": string;
    "/api/vworld/wfs": string;
    "/api/vworld/wms": string;
    "/api/vworld/wmts/{layer}/{tileMatrix}/{tileRow}/{tileCol}.{tileType}": string;
    "/api/vworld/wmts/capabilities": string;
    "/layer/cn/delete": string;
    "/layer/cn/list": string;
    "/layer/cn/select": string;
    "/layer/cn/update": string;
    "/layer/cn/find/fromEmdCode": string;
    "/layer/cn/find/fromGeometry": string;
    "/address/bld": string;
    "/address/coord": string;
    "/address/int": string;
    "/address/jibun": string;
    "/address/pnu": string;
    "/address/poi": string;
    "/address/road": string;
    "/address/jibun/list": string;
    "/address/road/link": string;
    "/refine/file": string;
    "/refine/single": string;
    "/refine/stream": string;
    "/geofile/delete": string;
    "/geofile/insert": string;
    "/geofile/list": string;
    "/geofile/select": string;
    "/geofile/update": string;
    "/layer/share/select/userId": string;
    "/webapp/tmplat/select/image-contents": string;
    "/geofile/select/detail": string;
    "/geoco/file": string;
    "/geoco/stream": string;
    "/administ/ctpv": string;
    "/administ/emd": string;
    "/administ/li": string;
    "/administ/sgg": string;
    "/administ/ctpv/list": string;
    "/administ/emd/list": string;
    "/administ/li/list": string;
    "/administ/sgg/list": string;
    "/convert/geojson-file-to-shp/download": string;
    "/convert/geojson-text-to-shp/download": string;
    "/anals/pttrn/density": string;
    "/anals/pttrn/hotspot": string;
    "/anals/pttrn/interpolatePoints": string;
    "/anals/proximity/buffer": string;
    "/anals/proximity/connectDestination": string;
    "/anals/proximity/drivingArea": string;
    "/anals/proximity/findNearestPoint": string;
    "/anals/proximity/findPath": string;
    "/anals/manage/ar": string;
    "/anals/manage/clustering": string;
    "/anals/manage/dsslve": string;
    "/anals/manage/dvsion": string;
    "/anals/manage/lt": string;
    "/anals/manage/merge": string;
    "/anals/manage/extrc/file": string;
    "/anals/manage/extrc/stream": string;
    "/anals/manage/ovrlay/erase": string;
    "/anals/manage/ovrlay/intsct": string;
    "/anals/manage/ovrlay/union": string;
    "/anals/manage/ovrlay/intsct/coverage": string;
    "/anals/sumry/ag": string;
    "/anals/sumry/center": string;
    "/anals/sumry/join": string;
    "/anals/sumry/nrby": string;
    "/anals/sumry/range": string;
    "/anals/lc/searchCenter": string;
    "/anals/lc/searchLegacy": string;
    "/anals/lc/searchNew": string;
    "/ntice/detail": string;
    "/ntice/seedState": string;
    "/layer/file/download": string;
    "/image/layer/file/donwload": string;
    "/image/layer/geotiff/download": string;
    "/upload/spatial/dxf/file": string;
    "/upload/spatial/dxf/stream": string;
    "/coord/single": string;
    "/coord/stream": string;
    "/coord/text": string;
    "/upload/spatial/db/republish": string;
    "/upload/spatial/db/table": string;
    "/upload/spatial/geojson/file": string;
    "/upload/spatial/geojson/stream": string;
    "/upload/spatial/geojson/text": string;
    "/upload/spatial/netcdf/file": string;
    "/upload/spatial/netcdf/stream": string;
    "/upload/spatial/shp/file": string;
    "/upload/spatial/shp/stream": string;
    "/upload/spatial/tif/file": string;
    "/upload/spatial/tif/stream": string;
    "/image/delete": string;
    "/image/insert": string;
    "/image/list": string;
    "/image/select": string;
    "/image/update": string;
    "/sld/stream": string;
    "/cmmn/code/detail": string;
    "/cmmn/code/group": string;
    "/instt/id": string;
    "/webapp/tmplat/share/delete": string;
    "/webapp/tmplat/share/insert": string;
    "/webapp/tmplat/share/select": string;
    "/webmap/share/update": string;
    "/webapp/tmplat/share/list": string;
    "/webmap/info/delete": string;
    "/lyrgrp/info/excel": string;
    "/webmap/info/insert": string;
    "/webmap/info/list": string;
    "/webmap/info/select": string;
    "/webmap/info/update": string;
    "/webmap/info/auth": string;
    "/lyrgrp/info/v2/list": string;
    "/layer/attributes": string;
    "/toc/layer/delete": string;
    "/toc/layer/insert": string;
    "/toc/layer/select": string;
    "/toc/layer/update": string;
    "/layer/attributes/aggregate": string;
    "/layer/attributes/bbox": string;
    "/layer/attributes/count": string;
    "/layer/attributes/select": string;
    "/layer/attributes/aggregate/select": string;
    "/layer/attributes/bbox/select": string;
    "/layer/column/unique/count/select": string;
    "/layer/flter/delete": string;
    "/layer/flter/insert": string;
    "/layer/flter/select": string;
    "/layer/style/delete": string;
    "/layer/style/insert": string;
    "/layer/style/select": string;
    "/layer/style/update": string;
    "/layer/symbol/delete": string;
    "/layer/symbol/insert": string;
    "/layer/symbol/select": string;
    "/layer/symbol/update": string;
    "/webapp/tmplat/thumbnail/delete": string;
    "/webmap/thumbnail/insert": string;
    "/webmap/thumbnail/select": string;
    "/webapp/tmplat/thumbnail/update": string;
    "/webmap/thumbnail/update/body": string;
    "/layer/column/update/type": string;
    "/webmap/info/update/code": string;
    "/layer/column/create": string;
    "/layer/column/insert": string;
    "/layer/column/range": string;
    "/layer/column/select": string;
    "/layer/column/unique": string;
    "/layer/column/unique/count": string;
    "/layer/column/unique/select": string;
    "/layer/popup/delete": string;
    "/layer/popup/insert": string;
    "/layer/popup/select": string;
    "/basemap/delete": string;
    "/basemap/insert": string;
    "/basemap/list": string;
    "/basemap/select": string;
    "/basemap/update": string;
    "/users/id": string;
    "/bkmk/delete": string;
    "/bkmk/insert": string;
    "/bkmk/list": string;
    "/bkmk/select": string;
    "/bkmk/update": string;
    "/usermap/opertntcn/delete": string;
    "/usermap/opertntcn/insert": string;
    "/usermap/opertntcn/list": string;
    "/usermap/opertntcn/queue": string;
    "/usermap/opertntcn/select": string;
    "/usermap/opertntcn/stats": string;
    "/usermap/opertntcn/update": string;
    "/usermap/opertntcn/stats/geocoding": string;
    "/api/registry/ownership/area": string;
    "/upload/image": string;
    "/layer/url/delete": string;
    "/layer/url/insert": string;
    "/layer/url/list": string;
    "/layer/url/select": string;
    "/layer/url/update": string;
    "/webapp/tmplat/auth/fullshare": string;
    "/webmap/info/insert/process": string;
    "/webmap/thumbnail/insert/body": string;
    "/webapp/tmplat/insert/image-contents": string;
    "/webapp/tmplat/allShare/delete": string;
    "/webapp/tmplat/allShare/insert": string;
    "/webapp/tmplat/auth": string;
    "/webapp/tmplat/delete": string;
    "/webapp/tmplat/insert": string;
    "/webapp/tmplat/list": string;
    "/webapp/tmplat/select": string;
    "/storage/activation": string;
    "/storage/detail": string;
    "/table/publish": string;
    "/table/republish": string;
    "/toc/group/delete": string;
    "/toc/group/insert": string;
    "/toc/group/select": string;
    "/toc/group/update": string;
    "/toc/group/lry/id": string;
    "/api/builds/build/heading/floor": string;
    "/api/builds/build/heading/ho": string;
    "/api/builds/build/register": string;
    "/api/builds/build/serial": string;
    "/api/registry/floor": string;
    "/api/registry/headings": string;
    "/api/registry/ownership": string;
    "/api/registry/general/headings": string;
    "/api/land/basic": string;
    "/api/land/characteristics": string;
    "/api/land/history": string;
    "/api/land/ownership": string;
    "/api/land/useplan": string;
    "/api/parcel/all": string;
    "/api/price/house/apt": string;
    "/api/price/house/ind": string;
    "/api/price/pclnd": string;
    "/api/landbundle/search/address": string;
};
//# sourceMappingURL=geonURL.d.ts.map