import { RequestContentType, ResponseContentType } from "./fetcher";
export type API_TYPE = "map" | "addrgeo" | "analysis" | "coord" | "publish" | "smt" | "estate" | "builder";
export declare const BASE_URL = "https://city.geon.kr/api/";
export declare const crtfckey = "UxizIdSqCePz93ViFt8ghZFFJuOzvUp0";
export declare const WMS_URL: string;
export declare const WFS_URL: string;
/**
 * 기본 API Config
 */
export interface APIConfig {
    /** 요청을 보낼 기본 URL */
    baseUrl?: string;
    /** crtfckey */
    crtfckey?: string;
    timeout?: number;
}
type ResponseCode = 0 | 200 | 940 | 999 | 960 | 980 | 981;
/**
 * General API Response interface
 *
 * @template T 응답 result 타입. 두 가지 타입을 지정 가능:
 * - result 필드 자체 타입 (`result: T`)
 * - resultList 의 아이템 타입 (`result: { resultList: T[] }`)
 */
export interface JsonResponse<T = unknown> {
    code: ResponseCode;
    message: string;
    result: string | T | {
        resultList?: T[];
        [key: string]: any;
    };
    pageInfo?: {
        numOfRows: number;
        pageNo: number;
        totalCount: string;
    };
    errorInfo?: {
        resultCode: string;
        message: string;
    };
}
export interface BlobResponse {
    headers: Headers;
    blob: Blob;
    code?: number;
    message?: string;
    result?: unknown;
}
/**
 * API endpoint factory helper
 *
 * @example
 * ```typescript
 * // 기본 request 타입이 any 로 설정된 api helper 생성
 * const api = apiHelper<any>({ type, baseUrl, crtfckey });
 * ```
 *
 * @template D default 요청 파라미터 타입
 */
export declare function apiHelper<D extends Record<string, any>>({ type, baseUrl, crtfckey, }: Omit<ApiUrlParams, "endpoint">): {
    /**
     * `fetcher.get` factory 함수
     *
     * @example
     * ```typescript
     * const api = apiHelper<any>({ ... });
     * // 요청 파라미터가 { crtfckey?: string } 이고, 응답 형태는 기본(APIResponse), endpoint 주소를 "/endpoint/url"로 갖는 fetcher.get
     * api.get<{ crtfckey?: string }>("/endpoint/url");
     * ```
     * @template P 요청 파라미터 타입
     * @template R 응답 타입
     *
     * @param endpoint endpoint url
     * @returns `Promise<R>`
     */
    get: <P extends Record<string, any> = D, R = JsonResponse<unknown>>(endpoint: string) => (params: P) => Promise<R>;
    /**
     * `fetcher.post` factory 함수
     *
     * @example
     * ```typescript
     * const api = apiHelper<any>({ ... });
     * // 요청 Body 가 { crtfckey?: string } 이고, 응답 형태는 기본(APIResponse), endpoint 주소를 "/endpoint/url"로 갖는 fetcher.post
     * api.post<{ crtfckey?: string }>("/endpoint/url");
     * ```
     * @template P 요청 body 타입
     * @template R 응답 타입
     *
     * @param endpoint endpoint url
     * @returns `Promise<R>`
     */
    post: <P extends Record<string, any> = D, R = JsonResponse<unknown>>(endpoint: string, requestContentType?: RequestContentType, responseContentType?: ResponseContentType) => (params: P) => Promise<R>;
};
/**
 * 요청 타입 헬퍼
 *
 * @example
 * ```typescript
 * const client: EstateClient = createEstateClient();
 * type EstateBuildingFloorRequest = APIRequestType<typeof client.building.floor>;
 * ```
 */
export type APIRequestType<T extends (...args: any) => any> = Parameters<T>[0];
/**
 * 응답 타입 헬퍼, Promise 가 resolve 된 상태의 타입 반환
 *
 * @example
 * ```typescript
 * const client: EstateClient = createEstateClient();
 * type EstateBuildingFloorResponse = APIResponseType<typeof client.building.floor>;
 * ```
 */
export type APIResponseType<T extends (...args: any) => any> = Awaited<ReturnType<T>>;
interface ApiUrlParams {
    endpoint: string;
    type: API_TYPE;
    baseUrl?: string;
    crtfckey?: string;
}
/**
 * GEON API URL을 생성하는 함수
 * @param {ApiUrlParams} params - API URL 생성 파라미터
 * @returns {string} 완성된 API URL
 * @example
 * ```typescript
 * // 기본 사용
 * const url1 = apiUrl({ endpoint: "/search", type: "addrgeo" });
 * // → "https://city.geon.kr/api/addrgeo/search?crtfckey=UxizIdSqCePz93ViFt8ghZFFJuOzvUp0"
 *
 * // 커스텀 설정 사용
 * const url2 = apiUrl({
 *   endpoint: "/address/bld",
 *   type: "addrgeo",
 *   baseUrl: "https://dev.geon.kr/api/",
 *   crtfckey: "custom-key"
 * });
 * // → "https://dev.geon.kr/api/addrgeo/address/bld?crtfckey=custom-key"
 * ```
 */
export declare const apiUrl: ({ endpoint, type, baseUrl, crtfckey: apiKey, }: ApiUrlParams) => string;
export {};
//# sourceMappingURL=geonAPI.d.ts.map