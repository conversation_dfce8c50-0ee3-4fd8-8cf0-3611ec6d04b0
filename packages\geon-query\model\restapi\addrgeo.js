import { apiHelper } from "../utils/geonAPI";
const type = "addrgeo";
// 동적 API 클라이언트 생성 함수
export function createGeonAddrgeoClient(config = {}) {
    const { baseUrl, crtfckey } = config;
    const api = apiHelper({ type, baseUrl, crtfckey });
    return {
        // 주소 API
        address: {
            /** 건물 주소 검색 */
            bld: api.get("/address/bld"),
            /** 통합 주소 검색 */
            int: api.get("/address/int"),
            /** 지번 주소 검색 */
            jibun: api.get("/address/jibun"),
            /** 도로명 주소 검색 */
            road: api.get("/address/road"),
            /** 도로 링크 기반 주소 검색 */
            roadLink: api.get("/address/road/link"),
            /** 좌표 → 주소 변환 */
            coord: api.get("/address/coord"),
            /** PNU 기반 주소 조회 */
            pnu: api.get("/address/pnu"),
            /** POI 주소 검색 */
            poi: api.get("/address/poi"),
            /** 기초구역번호 검색 */
            basic: api.get("/address/basic"),
        },
        // 행정구역 API
        administ: {
            /** 시도 검색 */
            ctpv: api.get("/administ/ctpv"),
            /** 시도 목록 검색 */
            ctpvList: api.get("/administ/ctpv/list"),
            /** 시군구 검색 */
            sgg: api.get("/administ/sgg"),
            /** 시군구 목록 검색 */
            sggList: api.get("/administ/sgg/list"),
            /** 읍면동 검색 */
            emd: api.get("/administ/emd"),
            /** 읍면동 목록 검색 */
            emdList: api.get("/administ/emd/list"),
            /** 리 검색 */
            li: api.get("/administ/li"),
            /** 리 목록 검색 */
            liList: api.get("/administ/li/list"),
        },
    };
}
export const defaultGeonAddrgeoClient = createGeonAddrgeoClient();
