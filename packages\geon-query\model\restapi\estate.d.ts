import type { APIConfig } from "../utils/geonAPI";
type EstateAPIRequest = {
    /** 검색 건 수 */
    numOfRows: number;
    /** 페이지 번호 */
    pageNo: number;
    /** 필지 고유 번호 */
    pnu: string;
    /** 인증키 */
    crtfckey?: string;
};
interface EstateAPIConfig extends APIConfig {
}
export type EstateClient = ReturnType<typeof createEstateClient>;
export declare function createEstateClient(config?: EstateAPIConfig): {
    /** 건물 조회(일필지) */
    building: {
        /** (Open-API) 건물 층 수 조회 */
        floor: (params: EstateAPIRequest & {
            /** 대지권 일련번호 */
            agbldgSn?: string;
            /** 동 이름 */
            buldDongNm?: string;
            /** 층 이름 */
            buldFloorNm?: string;
        }) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** (Open-API) 건물 호 수 조회 */
        ho: (params: EstateAPIRequest & {
            /** 대지권 일련번호 */
            agbldgSn?: string;
            /** 동 이름 */
            buldDongNm?: string;
            /** 층 이름 */
            buldFloorNm?: string;
            /** 호 이름 */
            buldHoNm?: string;
        }) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** (Open-API) 대지권 등록 목록 조회 */
        register: (params: EstateAPIRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** (Open-API) 건물일련번호 조회 */
        serial: (params: EstateAPIRequest & {
            /** 대지권 일련 번호 */
            agbldgSn?: string;
        }) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
    };
    /** 건축물 대장 정보 조회 */
    registry: {
        /** 층 별 개요 조회 */
        floor: (params: EstateAPIRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 총괄표제부 조회 */
        general: (params: EstateAPIRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 표제부 조회 */
        headings: (params: EstateAPIRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 전유부 조회 */
        ownership: (params: EstateAPIRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** 전유공유면적 조회 */
        area: (params: EstateAPIRequest & {
            /** 동 이름 */
            dongNm?: string;
            /** 호 이름 */
            hoNm?: string;
        }) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
    };
    /** 토지 조회(일필지) */
    land: {
        /** (Open-API) 토지 임야 목록 조회 */
        basic: (params: EstateAPIRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** (Open-API) 토지 특성 속성 조회 */
        characteristics: (params: EstateAPIRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** (Open-API) 토지 이동 이력 속성 조회 */
        history: (params: EstateAPIRequest & {
            /** 토지 이동 일자 시작일 */
            startDt?: string;
            /** 토지 이동 일자 종료일 */
            endDt?: string;
        }) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** (Open-API) 토지 소유 정보 속성 조회 */
        ownership: (params: EstateAPIRequest) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** (Open-API) 토지이용계획 속성 조회 */
        useplan: (params: EstateAPIRequest & {
            /** 저촉여부코드 */
            cnflcAt?: string;
            /** 용도지역지구 이름 */
            prposAreaDstrcCodeNm?: string;
        }) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
    };
    /** 일필지 종합 정보 */
    parcel: {
        /** (Open-API) 일필지 종합 정보 조회 */
        all: (params: Pick<EstateAPIRequest, "crtfckey" | "pnu">) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
    };
    /** 가격 정보 조회(일필지) */
    price: {
        /** (V-World) 공동주택가격 속성 조회 */
        apt: (params: EstateAPIRequest & {
            /** 동명 */
            dongNm?: string;
            /** 호명 */
            hoNm?: string;
            /** 기준 연도 */
            stdrYear?: string;
        }) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** (V-World) 개별주택가격 속성 조회 */
        ind: (params: EstateAPIRequest & {
            /** 기준 연도 */
            stdrYear?: string;
        }) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
        /** (V-World) 개별공시지가 속성 조회 */
        pclnd: (params: EstateAPIRequest & {
            /** 기준 연도 */
            stdrYear?: string;
        }) => Promise<import("../utils/geonAPI").JsonResponse<unknown>>;
    };
};
export {};
//# sourceMappingURL=estate.d.ts.map