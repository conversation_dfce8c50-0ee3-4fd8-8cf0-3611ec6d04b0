{"name": "web", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "biome lint .", "lint:fix": "biome lint --write .", "typecheck": "tsc --noEmit"}, "dependencies": {"@ai-sdk/openai": "^2.0.21", "@ai-sdk/openai-compatible": "^1.0.13", "@ai-sdk/react": "^2.0.23", "@geon-map/react-odf": "workspace:*", "@geon-map/react-ui": "workspace:*", "@geon-query/model": "workspace:*", "@geon-query/react-query": "workspace:*", "@modelcontextprotocol/sdk": "^1.13.2", "@workspace/ui": "workspace:*", "ai": "^5.0.23", "lucide-react": "^0.475.0", "next": "^15.4.5", "next-auth": "5.0.0-beta.29", "next-themes": "^0.4.6", "react": "^19.1.1", "react-dom": "^19.1.1", "sonner": "^2.0.7", "zod": "^4.1.5"}, "devDependencies": {"@types/node": "^20.19.9", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@workspace/typescript-config": "workspace:*", "typescript": "^5.9.2"}}