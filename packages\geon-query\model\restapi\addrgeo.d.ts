import { AddressBldRequest, AddressCoordRequest, AddressIntRequest, AddressJibunRequest, AddressPnuRequest, AddressPoiRequest, AddressRoadLinkRequest, AddressRoadRequest, AddressSearchResponse, AdministCtpvListRequest, AdministCtpvRequest, AdministEmdListRequest, AdministEmdRequest, AdministLiListRequest, AdministLiRequest, AdministResponse, AdministSggListRequest, AdministSggRequest } from "./type/addrgeo-type";
export interface AddrgeoAPIConfig {
    baseUrl?: string;
    crtfckey?: string;
    timeout?: number;
}
export type AddrgeoClient = ReturnType<typeof createGeonAddrgeoClient>;
export declare function createGeonAddrgeoClient(config?: AddrgeoAPIConfig): {
    address: {
        /** 건물 주소 검색 */
        bld: (params: AddressBldRequest) => Promise<AddressSearchResponse>;
        /** 통합 주소 검색 */
        int: (params: AddressIntRequest) => Promise<AddressSearchResponse>;
        /** 지번 주소 검색 */
        jibun: (params: AddressJibunRequest) => Promise<AddressSearchResponse>;
        /** 도로명 주소 검색 */
        road: (params: AddressRoadRequest) => Promise<AddressSearchResponse>;
        /** 도로 링크 기반 주소 검색 */
        roadLink: (params: AddressRoadLinkRequest) => Promise<AddressSearchResponse>;
        /** 좌표 → 주소 변환 */
        coord: (params: AddressCoordRequest) => Promise<AddressSearchResponse>;
        /** PNU 기반 주소 조회 */
        pnu: (params: AddressPnuRequest) => Promise<AddressSearchResponse>;
        /** POI 주소 검색 */
        poi: (params: AddressPoiRequest) => Promise<AddressSearchResponse>;
        /** 기초구역번호 검색 */
        basic: (params: AddressBldRequest) => Promise<AddressSearchResponse>;
    };
    administ: {
        /** 시도 검색 */
        ctpv: (params: AdministCtpvRequest) => Promise<AdministResponse>;
        /** 시도 목록 검색 */
        ctpvList: (params: AdministCtpvListRequest) => Promise<AdministResponse>;
        /** 시군구 검색 */
        sgg: (params: AdministSggRequest) => Promise<AdministResponse>;
        /** 시군구 목록 검색 */
        sggList: (params: AdministSggListRequest) => Promise<AdministResponse>;
        /** 읍면동 검색 */
        emd: (params: AdministEmdRequest) => Promise<AdministResponse>;
        /** 읍면동 목록 검색 */
        emdList: (params: AdministEmdListRequest) => Promise<AdministResponse>;
        /** 리 검색 */
        li: (params: AdministLiRequest) => Promise<AdministResponse>;
        /** 리 목록 검색 */
        liList: (params: AdministLiListRequest) => Promise<AdministResponse>;
    };
};
export declare const defaultGeonAddrgeoClient: {
    address: {
        /** 건물 주소 검색 */
        bld: (params: AddressBldRequest) => Promise<AddressSearchResponse>;
        /** 통합 주소 검색 */
        int: (params: AddressIntRequest) => Promise<AddressSearchResponse>;
        /** 지번 주소 검색 */
        jibun: (params: AddressJibunRequest) => Promise<AddressSearchResponse>;
        /** 도로명 주소 검색 */
        road: (params: AddressRoadRequest) => Promise<AddressSearchResponse>;
        /** 도로 링크 기반 주소 검색 */
        roadLink: (params: AddressRoadLinkRequest) => Promise<AddressSearchResponse>;
        /** 좌표 → 주소 변환 */
        coord: (params: AddressCoordRequest) => Promise<AddressSearchResponse>;
        /** PNU 기반 주소 조회 */
        pnu: (params: AddressPnuRequest) => Promise<AddressSearchResponse>;
        /** POI 주소 검색 */
        poi: (params: AddressPoiRequest) => Promise<AddressSearchResponse>;
        /** 기초구역번호 검색 */
        basic: (params: AddressBldRequest) => Promise<AddressSearchResponse>;
    };
    administ: {
        /** 시도 검색 */
        ctpv: (params: AdministCtpvRequest) => Promise<AdministResponse>;
        /** 시도 목록 검색 */
        ctpvList: (params: AdministCtpvListRequest) => Promise<AdministResponse>;
        /** 시군구 검색 */
        sgg: (params: AdministSggRequest) => Promise<AdministResponse>;
        /** 시군구 목록 검색 */
        sggList: (params: AdministSggListRequest) => Promise<AdministResponse>;
        /** 읍면동 검색 */
        emd: (params: AdministEmdRequest) => Promise<AdministResponse>;
        /** 읍면동 목록 검색 */
        emdList: (params: AdministEmdListRequest) => Promise<AdministResponse>;
        /** 리 검색 */
        li: (params: AdministLiRequest) => Promise<AdministResponse>;
        /** 리 목록 검색 */
        liList: (params: AdministLiListRequest) => Promise<AdministResponse>;
    };
};
//# sourceMappingURL=addrgeo.d.ts.map